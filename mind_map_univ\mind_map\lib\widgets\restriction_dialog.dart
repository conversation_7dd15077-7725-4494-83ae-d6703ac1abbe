import 'package:flutter/material.dart';
import '../services/restriction_service.dart';

class RestrictionDialog extends StatelessWidget {
  final Map<String, dynamic> restriction;
  final String actionName;

  const RestrictionDialog({
    super.key,
    required this.restriction,
    required this.actionName,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      title: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.amber.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.warning,
              color: Colors.amber.shade400,
              size: 24,
            ),
          ),
          const SizedBox(width: 12),
          const Expanded(
            child: Text(
              'إجراء مقيد',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.orange,
              ),
            ),
          ),
        ],
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.orange.withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.orange.withValues(alpha: 0.2),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'لا يمكنك $actionName حالياً',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.orange,
                  ),
                ),
                const SizedBox(height: 12),
                Text(
                  RestrictionService.getRestrictionMessage(restriction),
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey.shade700,
                    height: 1.5,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.blue.withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: Colors.blue.withValues(alpha: 0.2),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: Colors.blue,
                  size: 20,
                ),
                const SizedBox(width: 8),
                const Expanded(
                  child: Text(
                    'إذا كنت تعتقد أن هذا خطأ، يرجى التواصل مع الإدارة',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.blue,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          style: TextButton.styleFrom(
            foregroundColor: Colors.orange,
          ),
          child: const Text(
            'فهمت',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
        ),
      ],
    );
  }

  static Future<void> show(
    BuildContext context, 
    Map<String, dynamic> restriction, 
    String actionName
  ) async {
    return showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => RestrictionDialog(
        restriction: restriction,
        actionName: actionName,
      ),
    );
  }
}

// Widget مساعد للتحقق من التقييد قبل تنفيذ إجراء
class RestrictionChecker extends StatelessWidget {
  final String userId;
  final String restrictionType;
  final String actionName;
  final VoidCallback onAllowed;
  final Widget child;

  const RestrictionChecker({
    super.key,
    required this.userId,
    required this.restrictionType,
    required this.actionName,
    required this.onAllowed,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () async {
        // التحقق من التقييد أولاً
        final restriction = await RestrictionService.checkUserRestriction(
          userId, 
          restrictionType
        );

        if (restriction != null) {
          // عرض رسالة التقييد
          await RestrictionDialog.show(context, restriction, actionName);
        } else {
          // تنفيذ الإجراء
          onAllowed();
        }
      },
      child: child,
    );
  }
}

// Widget للأزرار مع فحص التقييد
class RestrictedButton extends StatelessWidget {
  final String userId;
  final String restrictionType;
  final String actionName;
  final VoidCallback onPressed;
  final Widget child;
  final ButtonStyle? style;

  const RestrictedButton({
    super.key,
    required this.userId,
    required this.restrictionType,
    required this.actionName,
    required this.onPressed,
    required this.child,
    this.style,
  });

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: () async {
        // التحقق من التقييد أولاً
        final restriction = await RestrictionService.checkUserRestriction(
          userId, 
          restrictionType
        );

        if (restriction != null) {
          // عرض رسالة التقييد
          await RestrictionDialog.show(context, restriction, actionName);
        } else {
          // تنفيذ الإجراء
          onPressed();
        }
      },
      style: style,
      child: child,
    );
  }
}

// Widget للحقول النصية مع فحص التقييد
class RestrictedTextField extends StatelessWidget {
  final String userId;
  final String restrictionType;
  final String actionName;
  final TextEditingController? controller;
  final InputDecoration? decoration;
  final int? maxLines;
  final Function(String)? onChanged;
  final Function(String)? onSubmitted;

  const RestrictedTextField({
    super.key,
    required this.userId,
    required this.restrictionType,
    required this.actionName,
    this.controller,
    this.decoration,
    this.maxLines,
    this.onChanged,
    this.onSubmitted,
  });

  @override
  Widget build(BuildContext context) {
    return TextField(
      controller: controller,
      decoration: decoration,
      maxLines: maxLines,
      onChanged: onChanged,
      onSubmitted: onSubmitted,
      onTap: () async {
        // التحقق من التقييد عند النقر على الحقل
        final restriction = await RestrictionService.checkUserRestriction(
          userId, 
          restrictionType
        );

        if (restriction != null) {
          // إزالة التركيز من الحقل
          FocusScope.of(context).unfocus();
          // عرض رسالة التقييد
          await RestrictionDialog.show(context, restriction, actionName);
        }
      },
    );
  }
}
