import 'package:flutter/material.dart';
import 'package:firebase_database/firebase_database.dart';
import '../models/user_model.dart';
import '../services/restriction_notification_service.dart';

class AdminUserActionsProvider extends ChangeNotifier {
  final DatabaseReference _database = FirebaseDatabase.instance.ref();
  
  List<UserModel> _users = [];
  bool _isLoading = false;
  String? _error;

  List<UserModel> get users => _users;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // تحميل جميع المستخدمين
  Future<void> loadUsers() async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final snapshot = await _database.child('users').get();

      if (snapshot.exists) {
        final Map<dynamic, dynamic> usersData = snapshot.value as Map<dynamic, dynamic>;
        _users = [];

        usersData.forEach((key, value) {
          try {
            print('🔍 معالجة المستخدم: $key');
            final userData = Map<String, dynamic>.from(value);
            userData['uid'] = key; // إضافة الـ uid

            // التأكد من وجود الحقول المطلوبة
            userData['firstName'] = userData['firstName'] ?? '';
            userData['lastName'] = userData['lastName'] ?? '';
            userData['email'] = userData['email'] ?? '';
            userData['university'] = userData['university'] ?? '';
            userData['major'] = userData['major'] ?? '';
            userData['bio'] = userData['bio'] ?? '';
            userData['isAdmin'] = userData['isAdmin'] ?? false;

            // تحويل التواريخ إلى strings للـ UserModel
            userData['createdAt'] = _parseDateTime(userData['createdAt']).toIso8601String();
            userData['updatedAt'] = _parseDateTime(userData['updatedAt']).toIso8601String();
            userData['lastActiveAt'] = _parseDateTime(userData['lastActiveAt']).toIso8601String();
            userData['birthDate'] = _parseDateTime(userData['birthDate']).toIso8601String();

            // تحويل القوائم
            userData['following'] = List<String>.from(userData['following'] ?? []);
            userData['followers'] = List<String>.from(userData['followers'] ?? []);

            // إضافة الحقول المطلوبة للخصوصية
            userData['isProfilePublic'] = userData['isProfilePublic'] ?? true;
            userData['showEmail'] = userData['showEmail'] ?? false;
            userData['showUniversity'] = userData['showUniversity'] ?? true;
            userData['showMajor'] = userData['showMajor'] ?? true;
            userData['defaultPublicMindMaps'] = userData['defaultPublicMindMaps'] ?? false;
            userData['allowComments'] = userData['allowComments'] ?? true;
            userData['allowCopying'] = userData['allowCopying'] ?? true;
            userData['allowFollowing'] = userData['allowFollowing'] ?? true;
            userData['showFollowers'] = userData['showFollowers'] ?? true;
            userData['showFollowing'] = userData['showFollowing'] ?? true;
            userData['showFollowersToFollowersOnly'] = userData['showFollowersToFollowersOnly'] ?? false;
            userData['showFollowingToFollowersOnly'] = userData['showFollowingToFollowersOnly'] ?? false;
            userData['notifyOnFollow'] = userData['notifyOnFollow'] ?? true;
            userData['notifyOnComment'] = userData['notifyOnComment'] ?? true;
            userData['notifyOnLike'] = userData['notifyOnLike'] ?? true;
            userData['notifyOnShare'] = userData['notifyOnShare'] ?? true;
            userData['notifyOnMention'] = userData['notifyOnMention'] ?? true;
            userData['emailNotifications'] = userData['emailNotifications'] ?? false;
            userData['pushNotifications'] = userData['pushNotifications'] ?? true;

            print('✅ بيانات المستخدم جاهزة: ${userData['firstName']} ${userData['lastName']}');
            final user = UserModel.fromMap(userData);
            _users.add(user);
            print('✅ تم إضافة المستخدم للقائمة');
          } catch (e, stackTrace) {
            print('❌ خطأ في تحويل بيانات المستخدم $key: $e');
            print('📍 Stack trace: $stackTrace');
          }
        });

        // ترتيب المستخدمين حسب تاريخ التسجيل (الأحدث أولاً)
        _users.sort((a, b) => b.createdAt.compareTo(a.createdAt));

        print('✅ تم تحميل ${_users.length} مستخدم');
      } else {
        _users = [];
        print('⚠️ لا توجد بيانات مستخدمين في Firebase');
      }
    } catch (e) {
      _error = 'خطأ في تحميل المستخدمين: $e';
      print('❌ خطأ في تحميل المستخدمين: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // حظر مستخدم
  Future<bool> banUser(String userId, String reason) async {
    try {
      final banData = {
        'isBanned': true,
        'banReason': reason,
        'bannedAt': DateTime.now().toIso8601String(),
        'bannedBy': 'admin', // TODO: استخدام معرف المدير الحالي
      };

      await _database.child('users/$userId').update(banData);
      await _database.child('user_actions/$userId/ban').set(banData);

      // تحديث المستخدم في القائمة المحلية
      final userIndex = _users.indexWhere((user) => user.uid == userId);
      if (userIndex != -1) {
        // TODO: تحديث حالة المستخدم محلياً
        notifyListeners();
      }

      print('✅ تم حظر المستخدم: $userId');
      return true;
    } catch (e) {
      _error = 'خطأ في حظر المستخدم: $e';
      print('❌ خطأ في حظر المستخدم: $e');
      notifyListeners();
      return false;
    }
  }

  // تقييد مستخدم مؤقتاً مع تحديد نوع التقييد
  Future<bool> restrictUser(String userId, int days, String reason, List<String> restrictionTypes) async {
    try {
      final restrictionEndDate = DateTime.now().add(Duration(days: days));

      final restrictionData = {
        'isRestricted': true,
        'restrictionReason': reason,
        'restrictedAt': DateTime.now().toIso8601String(),
        'restrictionEndDate': restrictionEndDate.toIso8601String(),
        'restrictionDays': days,
        'restrictionTypes': restrictionTypes, // أنواع التقييد المحددة
        'restrictedBy': 'admin', // TODO: استخدام معرف المدير الحالي
      };

      await _database.child('users/$userId').update(restrictionData);

      // تسجيل الإجراء في تاريخ المستخدم مع التفاصيل
      final restrictionTypesText = restrictionTypes.join('، ');
      final actionData = {
        'actionType': 'restrict',
        'actionDate': DateTime.now().toIso8601String(),
        'actionBy': 'admin',
        'details': 'تم تقييد المستخدم لمدة $days أيام على: $restrictionTypesText. السبب: $reason',
        'restrictionTypes': restrictionTypes,
        'restrictionDays': days,
        'restrictionReason': reason,
      };

      await _database.child('user_actions/$userId').push().set(actionData);

      // إرسال إشعار التقييد للمستخدم
      await RestrictionNotificationService.sendRestrictionNotification(
        userId: userId,
        reason: reason,
        days: days,
        restrictionTypes: restrictionTypes,
        restrictedBy: 'الإدارة',
      );

      // إعادة تحميل بيانات المستخدمين لتحديث الحالة
      await loadUsers();

      print('✅ تم تقييد المستخدم: $userId لمدة $days أيام على: $restrictionTypesText');
      return true;
    } catch (e) {
      _error = 'خطأ في تقييد المستخدم: $e';
      print('❌ خطأ في تقييد المستخدم: $e');
      notifyListeners();
      return false;
    }
  }

  // إرسال تحذير لمستخدم
  Future<bool> warnUser(String userId, String message) async {
    try {
      final warningData = {
        'message': message,
        'sentAt': DateTime.now().toIso8601String(),
        'sentBy': 'admin', // TODO: استخدام معرف المدير الحالي
        'isRead': false,
      };

      // إضافة التحذير لقائمة التحذيرات
      await _database.child('user_warnings/$userId').push().set(warningData);

      // إرسال إشعار التحذير للمستخدم
      await RestrictionNotificationService.sendWarningNotification(
        userId: userId,
        reason: message,
        warnedBy: 'الإدارة',
      );

      print('✅ تم إرسال تحذير للمستخدم: $userId');
      return true;
    } catch (e) {
      _error = 'خطأ في إرسال التحذير: $e';
      print('❌ خطأ في إرسال التحذير: $e');
      notifyListeners();
      return false;
    }
  }

  // إلغاء حظر مستخدم
  Future<bool> unbanUser(String userId) async {
    try {
      final unbanData = {
        'isBanned': false,
        'banReason': null,
        'bannedAt': null,
        'bannedBy': null,
        'unbannedAt': DateTime.now().toIso8601String(),
        'unbannedBy': 'admin', // TODO: استخدام معرف المدير الحالي
      };

      await _database.child('users/$userId').update(unbanData);
      await _database.child('user_actions/$userId/unban').set({
        'unbannedAt': DateTime.now().toIso8601String(),
        'unbannedBy': 'admin',
      });

      // تحديث المستخدم في القائمة المحلية
      final userIndex = _users.indexWhere((user) => user.uid == userId);
      if (userIndex != -1) {
        // TODO: تحديث حالة المستخدم محلياً
        notifyListeners();
      }

      print('✅ تم إلغاء حظر المستخدم: $userId');
      return true;
    } catch (e) {
      _error = 'خطأ في إلغاء حظر المستخدم: $e';
      print('❌ خطأ في إلغاء حظر المستخدم: $e');
      notifyListeners();
      return false;
    }
  }

  // إلغاء تقييد مستخدم
  Future<bool> unrestrictUser(String userId) async {
    try {
      final unrestrictData = {
        'isRestricted': false,
        'restrictionReason': null,
        'restrictedAt': null,
        'restrictionEndDate': null,
        'restrictionDays': null,
        'restrictionTypes': null,
        'restrictedBy': null,
        'unrestrictedAt': DateTime.now().toIso8601String(),
        'unrestrictedBy': 'admin', // TODO: استخدام معرف المدير الحالي
      };

      await _database.child('users/$userId').update(unrestrictData);

      // تسجيل الإجراء في تاريخ المستخدم
      final actionData = {
        'actionType': 'unrestrict',
        'actionDate': DateTime.now().toIso8601String(),
        'actionBy': 'admin',
        'details': 'تم إلغاء تقييد المستخدم',
      };

      await _database.child('user_actions/$userId').push().set(actionData);

      // إرسال إشعار إلغاء التقييد للمستخدم
      await RestrictionNotificationService.sendUnrestrictionNotification(
        userId: userId,
        unrestrictedBy: 'الإدارة',
        isAutomatic: false,
      );

      // إعادة تحميل بيانات المستخدمين لتحديث الحالة
      await loadUsers();

      print('✅ تم إلغاء تقييد المستخدم: $userId');
      return true;
    } catch (e) {
      _error = 'خطأ في إلغاء تقييد المستخدم: $e';
      print('❌ خطأ في إلغاء تقييد المستخدم: $e');
      notifyListeners();
      return false;
    }
  }

  // الحصول على إحصائيات المستخدم
  Future<Map<String, int>> getUserStats(String userId) async {
    try {
      final stats = <String, int>{
        'warnings': 0,
      };

      // عدد التحذيرات فقط
      try {
        final warningsSnapshot = await _database
            .child('user_warnings/$userId')
            .get();
        if (warningsSnapshot.exists) {
          stats['warnings'] = (warningsSnapshot.value as Map).length;
        }
      } catch (e) {
        print('❌ خطأ في تحميل التحذيرات: $e');
      }

      print('📊 إحصائيات المستخدم $userId: $stats');
      return stats;
    } catch (e) {
      print('❌ خطأ في تحميل إحصائيات المستخدم: $e');
      return {
        'warnings': 0,
      };
    }
  }

  // البحث في المستخدمين
  List<UserModel> searchUsers(String query) {
    if (query.isEmpty) return _users;
    
    return _users.where((user) {
      return user.fullName.toLowerCase().contains(query.toLowerCase()) ||
             user.email.toLowerCase().contains(query.toLowerCase()) ||
             user.university.toLowerCase().contains(query.toLowerCase()) ||
             user.major.toLowerCase().contains(query.toLowerCase());
    }).toList();
  }

  // الحصول على تاريخ إجراءات المستخدم
  Future<List<Map<String, dynamic>>> getUserActionHistory(String userId) async {
    try {
      final actionsSnapshot = await _database.child('user_actions/$userId').get();
      if (!actionsSnapshot.exists) {
        return [];
      }

      final actionsData = actionsSnapshot.value as Map<dynamic, dynamic>;
      final actions = <Map<String, dynamic>>[];

      actionsData.forEach((key, value) {
        if (value is Map) {
          final action = Map<String, dynamic>.from(value);
          action['id'] = key;
          actions.add(action);
        }
      });

      // ترتيب الإجراءات حسب التاريخ (الأحدث أولاً)
      actions.sort((a, b) {
        final dateA = DateTime.parse(a['actionDate'] ?? DateTime.now().toIso8601String());
        final dateB = DateTime.parse(b['actionDate'] ?? DateTime.now().toIso8601String());
        return dateB.compareTo(dateA);
      });

      return actions;
    } catch (e) {
      print('❌ خطأ في تحميل تاريخ الإجراءات: $e');
      return [];
    }
  }

  // التحقق من حالة تقييد المستخدم
  bool isUserRestricted(UserModel user) {
    try {
      // التحقق من وجود تقييد نشط
      if (user.isRestricted == true) {
        final restrictionEndDate = user.restrictionEndDate;
        if (restrictionEndDate != null && restrictionEndDate.isAfter(DateTime.now())) {
          return true;
        }
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  // الحصول على أنواع التقييد النشطة
  List<String> getActiveRestrictionTypes(UserModel user) {
    try {
      if (isUserRestricted(user)) {
        return user.restrictionTypes ?? [];
      }
      return [];
    } catch (e) {
      return [];
    }
  }

  // مسح الخطأ
  void clearError() {
    _error = null;
    notifyListeners();
  }

  // دالة مساعدة لتحويل التواريخ
  DateTime _parseDateTime(dynamic dateValue) {
    if (dateValue == null) {
      return DateTime.now();
    }

    if (dateValue is DateTime) {
      return dateValue;
    }

    if (dateValue is String) {
      try {
        return DateTime.parse(dateValue);
      } catch (e) {
        print('❌ خطأ في تحويل التاريخ: $dateValue');
        return DateTime.now();
      }
    }

    if (dateValue is int) {
      // إذا كان timestamp
      return DateTime.fromMillisecondsSinceEpoch(dateValue);
    }

    return DateTime.now();
  }
}
