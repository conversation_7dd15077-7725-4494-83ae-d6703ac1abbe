import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/admin_statistics_provider.dart';
import '../models/admin_statistics.dart';
import 'admin_statistics_sections.dart';

class AdminStatisticsScreen extends StatefulWidget {
  const AdminStatisticsScreen({super.key});

  @override
  State<AdminStatisticsScreen> createState() => _AdminStatisticsScreenState();
}

class _AdminStatisticsScreenState extends State<AdminStatisticsScreen> {
  @override
  void initState() {
    super.initState();
    // تحميل الإحصائيات عند فتح الشاشة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<AdminStatisticsProvider>().loadStatistics();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: const Text(
          'إحصائيات التطبيق',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.indigo.shade600,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: () {
              context.read<AdminStatisticsProvider>().refreshStatistics();
            },
            icon: const Icon(Icons.refresh_rounded),
            tooltip: 'تحديث الإحصائيات',
          ),
        ],
      ),
      body: Consumer<AdminStatisticsProvider>(
        builder: (context, provider, child) {
          if (provider.isLoading) {
            return const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text(
                    'جاري تحميل الإحصائيات...',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            );
          }

          if (provider.error != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Colors.red.shade400,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    provider.error!,
                    style: const TextStyle(
                      fontSize: 16,
                      color: Colors.red,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      provider.clearError();
                      provider.loadStatistics();
                    },
                    child: const Text('إعادة المحاولة'),
                  ),
                ],
              ),
            );
          }

          return RefreshIndicator(
            onRefresh: () => provider.refreshStatistics(),
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // إحصائيات عامة
                  _buildOverviewSection(provider.statistics),
                  const SizedBox(height: 24),
                  
                  // إحصائيات المستخدمين
                  _buildUserStatisticsSection(provider.statistics),
                  const SizedBox(height: 24),
                  
                  // إحصائيات المخططات الذهنية
                  AdminStatisticsSections.buildMindMapStatisticsSection(provider.statistics),
                  const SizedBox(height: 24),

                  // إحصائيات المنشورات
                  AdminStatisticsSections.buildPostStatisticsSection(provider.statistics),
                  const SizedBox(height: 24),

                  // إحصائيات طلبات التعديل
                  AdminStatisticsSections.buildEditRequestStatisticsSection(provider.statistics),
                  const SizedBox(height: 24),

                  // إحصائيات القوالب
                  AdminStatisticsSections.buildTemplateStatisticsSection(provider.statistics),
                  const SizedBox(height: 100),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  // قسم الإحصائيات العامة
  Widget _buildOverviewSection(AdminStatistics stats) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.dashboard_rounded,
                  color: Colors.indigo.shade600,
                  size: 28,
                ),
                const SizedBox(width: 12),
                const Text(
                  'نظرة عامة',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'إجمالي المستخدمين',
                    stats.totalUsers.toString(),
                    Icons.people_rounded,
                    Colors.blue,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    'إجمالي المخططات',
                    stats.totalMindMaps.toString(),
                    Icons.account_tree_rounded,
                    Colors.green,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'إجمالي المنشورات',
                    stats.totalPosts.toString(),
                    Icons.post_add_rounded,
                    Colors.orange,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    'إجمالي التعليقات',
                    stats.totalComments.toString(),
                    Icons.comment_rounded,
                    Colors.purple,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // بطاقة إحصائية صغيرة
  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: color,
            size: 32,
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade600,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // قسم إحصائيات المستخدمين
  Widget _buildUserStatisticsSection(AdminStatistics stats) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.people_rounded,
                  color: Colors.blue.shade600,
                  size: 28,
                ),
                const SizedBox(width: 12),
                const Text(
                  'إحصائيات المستخدمين',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),

            // المستخدمين النشطين
            _buildSubSection(
              'المستخدمين النشطين',
              [
                _buildInfoRow('اليوم', stats.activeUsersToday.toString()),
                _buildInfoRow('هذا الأسبوع', stats.activeUsersThisWeek.toString()),
                _buildInfoRow('هذا الشهر', stats.activeUsersThisMonth.toString()),
              ],
            ),
            const SizedBox(height: 16),

            // المستخدمين الجدد
            _buildSubSection(
              'المستخدمين الجدد',
              [
                _buildInfoRow('اليوم', stats.newUsersToday.toString()),
                _buildInfoRow('هذا الأسبوع', stats.newUsersThisWeek.toString()),
                _buildInfoRow('هذا الشهر', stats.newUsersThisMonth.toString()),
              ],
            ),
            const SizedBox(height: 16),

            // متوسطات التفاعل
            _buildSubSection(
              'متوسطات التفاعل',
              [
                _buildInfoRow('متوسط المتابعين', stats.averageFollowersPerUser.toStringAsFixed(1)),
                _buildInfoRow('متوسط المتابَعين', stats.averageFollowingPerUser.toStringAsFixed(1)),
                _buildInfoRow('متوسط المنشورات', stats.averagePostsPerUser.toStringAsFixed(1)),
                _buildInfoRow('متوسط المخططات', stats.averageMindMapsPerUser.toStringAsFixed(1)),
              ],
            ),

            // توزيع حسب الجامعة
            if (stats.usersByUniversity.isNotEmpty) ...[
              const SizedBox(height: 16),
              _buildDistributionSection('توزيع المستخدمين حسب الجامعة', stats.usersByUniversity),
            ],

            // توزيع حسب التخصص
            if (stats.usersByMajor.isNotEmpty) ...[
              const SizedBox(height: 16),
              _buildDistributionSection('توزيع المستخدمين حسب التخصص', stats.usersByMajor),
            ],
          ],
        ),
      ),
    );
  }

  // قسم فرعي
  Widget _buildSubSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.grey.shade700,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.grey.shade50,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: Colors.grey.shade200,
              width: 1,
            ),
          ),
          child: Column(
            children: children,
          ),
        ),
      ],
    );
  }

  // صف معلومات
  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade600,
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  // قسم التوزيع
  Widget _buildDistributionSection(String title, Map<String, int> data) {
    final sortedEntries = data.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.grey.shade700,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.grey.shade50,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: Colors.grey.shade200,
              width: 1,
            ),
          ),
          child: Column(
            children: sortedEntries.take(5).map((entry) {
              return _buildInfoRow(entry.key, entry.value.toString());
            }).toList(),
          ),
        ),
      ],
    );
  }
}