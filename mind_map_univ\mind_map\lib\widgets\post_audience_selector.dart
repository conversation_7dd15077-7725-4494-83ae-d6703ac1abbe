import 'package:flutter/material.dart';
import '../models/post.dart';

class PostAudienceSelector extends StatefulWidget {
  final PostAudience initialAudience;
  final Function(PostAudience) onAudienceChanged;

  const PostAudienceSelector({
    Key? key,
    this.initialAudience = PostAudience.everyone,
    required this.onAudienceChanged,
  }) : super(key: key);

  @override
  State<PostAudienceSelector> createState() => _PostAudienceSelectorState();
}

class _PostAudienceSelectorState extends State<PostAudienceSelector> {
  late PostAudience selectedAudience;

  @override
  void initState() {
    super.initState();
    selectedAudience = widget.initialAudience;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // العنوان
          Row(
            children: [
              Icon(Icons.people, color: Colors.blue.shade700, size: 24),
              const SizedBox(width: 12),
              const Text(
                'اختر جمهور المنشور',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),

          // خيارات الجمهور
          _buildAudienceOption(
            audience: PostAudience.everyone,
            icon: Icons.public,
            title: 'للجميع',
            subtitle: 'يمكن لأي شخص رؤية هذا المنشور',
            color: Colors.green,
          ),
          const SizedBox(height: 12),
          
          _buildAudienceOption(
            audience: PostAudience.followersOnly,
            icon: Icons.people,
            title: 'لمتابعيني فقط',
            subtitle: 'فقط الأشخاص الذين يتابعونك يمكنهم رؤية هذا المنشور',
            color: Colors.blue,
          ),
          const SizedBox(height: 12),
          
          _buildAudienceOption(
            audience: PostAudience.mutualFollowers,
            icon: Icons.people_alt,
            title: 'للمتابعين المتبادلين',
            subtitle: 'فقط الأشخاص الذين تتابعهم ويتابعونك',
            color: Colors.purple,
          ),
          
          const SizedBox(height: 24),
          
          // أزرار التحكم
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: () => Navigator.of(context).pop(),
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text('إلغاء'),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ElevatedButton(
                  onPressed: () {
                    widget.onAudienceChanged(selectedAudience);
                    Navigator.of(context).pop();
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue.shade700,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text('تأكيد'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAudienceOption({
    required PostAudience audience,
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
  }) {
    final isSelected = selectedAudience == audience;
    
    return GestureDetector(
      onTap: () {
        setState(() {
          selectedAudience = audience;
        });
      },
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isSelected ? color.withOpacity(0.1) : Colors.grey.shade50,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? color : Colors.grey.shade300,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: isSelected ? color : Colors.grey.shade400,
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon,
                color: Colors.white,
                size: 20,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: isSelected ? color : Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ),
            if (isSelected)
              Icon(
                Icons.check_circle,
                color: color,
                size: 24,
              ),
          ],
        ),
      ),
    );
  }
}

// دالة مساعدة لعرض الشاشة المنبثقة
Future<PostAudience?> showPostAudienceSelector({
  required BuildContext context,
  PostAudience initialAudience = PostAudience.everyone,
}) async {
  PostAudience? selectedAudience;
  
  await showModalBottomSheet<void>(
    context: context,
    isScrollControlled: true,
    backgroundColor: Colors.transparent,
    builder: (BuildContext context) {
      return PostAudienceSelector(
        initialAudience: initialAudience,
        onAudienceChanged: (audience) {
          selectedAudience = audience;
        },
      );
    },
  );
  
  return selectedAudience;
}

// دوال مساعدة للحصول على معلومات الجمهور
extension PostAudienceExtension on PostAudience {
  String get displayName {
    switch (this) {
      case PostAudience.everyone:
        return 'للجميع';
      case PostAudience.followersOnly:
        return 'لمتابعيني فقط';
      case PostAudience.mutualFollowers:
        return 'للمتابعين المتبادلين';
    }
  }

  IconData get icon {
    switch (this) {
      case PostAudience.everyone:
        return Icons.public;
      case PostAudience.followersOnly:
        return Icons.people;
      case PostAudience.mutualFollowers:
        return Icons.people_alt;
    }
  }

  Color get color {
    switch (this) {
      case PostAudience.everyone:
        return Colors.green;
      case PostAudience.followersOnly:
        return Colors.blue;
      case PostAudience.mutualFollowers:
        return Colors.purple;
    }
  }
}
