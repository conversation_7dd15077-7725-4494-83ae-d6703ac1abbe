import 'dart:ui';
import 'package:uuid/uuid.dart';

/// أنواع الخطوط المختلفة
enum ConnectionType {
  straight,    // خط مستقيم
  curved,      // خط منحني
  stepped,     // خط متدرج
  dashed,      // خط متقطع
}

/// أنماط الأسهم
enum ArrowStyle {
  none,        // بدون سهم
  simple,      // سهم بسيط
  filled,      // سهم مملوء
  double,      // سهم مزدوج
}

/// أنماط الخطوط
enum ConnectionStyle {
  solid,       // خط مصمت
  dashed,      // خط متقطع
  dotted,      // خط منقط
}

/// نموذج الاتصال بين العقد
class MindMapConnection {
  final String id;
  final String fromNodeId;
  final String toNodeId;
  String label;
  Color color;
  double thickness;
  ConnectionType type;
  ConnectionStyle style;
  ArrowStyle arrowStyle;
  bool isVisible;
  DateTime createdAt;
  DateTime updatedAt;

  MindMapConnection({
    String? id,
    required this.fromNodeId,
    required this.toNodeId,
    this.label = '',
    this.color = const Color(0xFF2196F3),
    this.thickness = 2.0,
    this.type = ConnectionType.curved,
    this.style = ConnectionStyle.solid,
    this.arrowStyle = ArrowStyle.simple,
    this.isVisible = true,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : id = id ?? const Uuid().v4(),
       createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'fromNodeId': fromNodeId,
      'toNodeId': toNodeId,
      'label': label,
      'color': color.value.toRadixString(16),
      'thickness': thickness,
      'type': type.name,
      'style': style.name,
      'arrowStyle': arrowStyle.name,
      'isVisible': isVisible,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  /// إنشاء من JSON
  factory MindMapConnection.fromJson(Map<String, dynamic> json) {
    return MindMapConnection(
      id: json['id'] ?? '',
      fromNodeId: json['fromNodeId'] ?? '',
      toNodeId: json['toNodeId'] ?? '',
      label: json['label'] ?? '',
      color: json['color'] != null
          ? Color(int.parse(json['color'], radix: 16))
          : const Color(0xFF2196F3),
      thickness: (json['thickness'] ?? 2.0).toDouble(),
      type: ConnectionType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => ConnectionType.curved,
      ),
      style: ConnectionStyle.values.firstWhere(
        (e) => e.name == json['style'],
        orElse: () => ConnectionStyle.solid,
      ),
      arrowStyle: ArrowStyle.values.firstWhere(
        (e) => e.name == json['arrowStyle'],
        orElse: () => ArrowStyle.simple,
      ),
      isVisible: json['isVisible'] ?? true,
      createdAt: json['createdAt'] != null
          ? DateTime.parse(json['createdAt'])
          : DateTime.now(),
      updatedAt: json['updatedAt'] != null
          ? DateTime.parse(json['updatedAt'])
          : DateTime.now(),
    );
  }

  /// نسخ مع تعديلات
  MindMapConnection copyWith({
    String? label,
    Color? color,
    double? thickness,
    ConnectionType? type,
    ArrowStyle? arrowStyle,
    bool? isVisible,
  }) {
    return MindMapConnection(
      id: id,
      fromNodeId: fromNodeId,
      toNodeId: toNodeId,
      label: label ?? this.label,
      color: color ?? this.color,
      thickness: thickness ?? this.thickness,
      type: type ?? this.type,
      arrowStyle: arrowStyle ?? this.arrowStyle,
      isVisible: isVisible ?? this.isVisible,
      createdAt: createdAt,
      updatedAt: DateTime.now(),
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MindMapConnection && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'MindMapConnection(id: $id, from: $fromNodeId, to: $toNodeId, label: $label)';
  }
}
