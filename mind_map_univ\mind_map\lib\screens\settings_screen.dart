import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/auth_provider.dart';
import 'edit_profile_screen.dart';
import 'change_password_screen.dart';
import 'privacy_settings_screen.dart';
import '../widgets/logout_loading_screen.dart';
import '../widgets/connectivity_wrapper.dart';

class SettingsScreen extends StatefulWidget {
  final VoidCallback? onNavigateToProfile;

  const SettingsScreen({
    super.key,
    this.onNavigateToProfile,
  });

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  @override
  Widget build(BuildContext context) {
    return ConnectivityWrapper(
      child: Scaffold(
      appBar: AppBar(
        title: const Text(
          'الإعدادات',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.blue.shade700,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      backgroundColor: Colors.grey.shade50,
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          // قسم الحساب
          _buildSettingsSection(
            title: 'الحساب',
            icon: Icons.person,
            children: [
              // بطاقة المستخدم
              Consumer<AuthProvider>(
                builder: (context, authProvider, child) {
                  final user = authProvider.user;
                  final userModel = authProvider.userModel;
                  final isAnonymous = user?.isAnonymous ?? false;

                  return _buildUserProfileCard(
                    isAnonymous: isAnonymous,
                    userModel: userModel,
                    onTap: () {
                      // التنقل إلى البروفايل
                      if (widget.onNavigateToProfile != null) {
                        widget.onNavigateToProfile!();
                      }
                    },
                  );
                },
              ),
              const Divider(),
              Consumer<AuthProvider>(
                builder: (context, authProvider, child) {
                  final userModel = authProvider.userModel;
                  final isAnonymous = authProvider.user?.isAnonymous ?? false;

                  return _buildSettingsItem(
                    icon: Icons.edit,
                    title: 'تعديل الملف الشخصي',
                    subtitle: isAnonymous
                      ? 'سجل دخولك أولاً لتعديل ملفك الشخصي'
                      : 'تحديث معلوماتك الشخصية',
                    onTap: isAnonymous
                      ? () => _showLoginRequiredDialog()
                      : userModel != null
                        ? () => _navigateToEditProfile(userModel)
                        : null,
                  );
                },
              ),
              Consumer<AuthProvider>(
                builder: (context, authProvider, child) {
                  final isAnonymous = authProvider.user?.isAnonymous ?? false;

                  return _buildSettingsItem(
                    icon: Icons.lock,
                    title: 'تغيير كلمة المرور',
                    subtitle: isAnonymous
                      ? 'سجل دخولك أولاً لتغيير كلمة المرور'
                      : 'تحديث كلمة المرور الخاصة بك',
                    onTap: isAnonymous
                      ? () => _showLoginRequiredDialog()
                      : () => _navigateToChangePassword(),
                  );
                },
              ),
              Consumer<AuthProvider>(
                builder: (context, authProvider, child) {
                  final isAnonymous = authProvider.user?.isAnonymous ?? false;

                  return _buildSettingsItem(
                    icon: Icons.privacy_tip,
                    title: 'إعدادات الخصوصية',
                    subtitle: isAnonymous
                      ? 'سجل دخولك أولاً لإدارة إعدادات الخصوصية'
                      : 'التحكم في من يرى معلوماتك',
                    onTap: isAnonymous
                      ? () => _showLoginRequiredDialog()
                      : () => _navigateToPrivacySettings(),
                  );
                },
              ),
              const Divider(),
              // زر تسجيل الخروج
              Consumer<AuthProvider>(
                builder: (context, authProvider, child) {
                  return _buildSettingsItem(
                    icon: Icons.logout,
                    title: 'تسجيل الخروج',
                    subtitle: 'الخروج من حسابك الحالي',
                    textColor: Colors.red,
                    trailing: const Icon(Icons.arrow_forward_ios, size: 16, color: Colors.red),
                    onTap: () => _showSignOutDialog(authProvider),
                  );
                },
              ),
            ],
          ),

          const SizedBox(height: 20),

          // قسم التطبيق
          _buildSettingsSection(
            title: 'التطبيق',
            icon: Icons.settings,
            children: [
              _buildSettingsItem(
                icon: Icons.dark_mode,
                title: 'الوضع الليلي',
                subtitle: 'تفعيل/إلغاء الوضع الليلي',
                trailing: Switch(
                  value: false, // TODO: ربط بحالة الوضع الليلي
                  onChanged: (value) {
                    // TODO: تغيير الوضع الليلي
                    _showComingSoonSnackBar('الوضع الليلي');
                  },
                ),
                onTap: null,
              ),
              _buildSettingsItem(
                icon: Icons.language,
                title: 'اللغة',
                subtitle: 'العربية',
                onTap: () {
                  // TODO: فتح شاشة اختيار اللغة
                  _showComingSoonSnackBar('تغيير اللغة');
                },
              ),
              _buildSettingsItem(
                icon: Icons.notifications,
                title: 'الإشعارات',
                subtitle: 'إدارة إعدادات الإشعارات',
                onTap: () {
                  // TODO: فتح شاشة إعدادات الإشعارات
                  _showComingSoonSnackBar('إعدادات الإشعارات');
                },
              ),
            ],
          ),

          const SizedBox(height: 20),

          // قسم البيانات
          _buildSettingsSection(
            title: 'البيانات',
            icon: Icons.storage,
            children: [
              _buildSettingsItem(
                icon: Icons.backup,
                title: 'النسخ الاحتياطي',
                subtitle: 'حفظ بياناتك في السحابة',
                onTap: () {
                  // TODO: فتح شاشة النسخ الاحتياطي
                  _showComingSoonSnackBar('النسخ الاحتياطي');
                },
              ),
              _buildSettingsItem(
                icon: Icons.download,
                title: 'تصدير البيانات',
                subtitle: 'تحميل نسخة من بياناتك',
                onTap: () {
                  // TODO: تصدير البيانات
                  _showComingSoonSnackBar('تصدير البيانات');
                },
              ),
              _buildSettingsItem(
                icon: Icons.delete_forever,
                title: 'حذف الحساب',
                subtitle: 'حذف حسابك نهائياً',
                textColor: Colors.red,
                onTap: () {
                  _showDeleteAccountDialog();
                },
              ),
            ],
          ),

          const SizedBox(height: 20),

          // قسم المساعدة
          _buildSettingsSection(
            title: 'المساعدة والدعم',
            icon: Icons.help,
            children: [
              _buildSettingsItem(
                icon: Icons.help_outline,
                title: 'الأسئلة الشائعة',
                subtitle: 'إجابات للأسئلة الشائعة',
                onTap: () {
                  // TODO: فتح شاشة الأسئلة الشائعة
                  _showComingSoonSnackBar('الأسئلة الشائعة');
                },
              ),
              _buildSettingsItem(
                icon: Icons.contact_support,
                title: 'تواصل معنا',
                subtitle: 'إرسال رسالة للدعم الفني',
                onTap: () {
                  // TODO: فتح شاشة التواصل
                  _showComingSoonSnackBar('التواصل مع الدعم');
                },
              ),
              _buildSettingsItem(
                icon: Icons.info,
                title: 'حول التطبيق',
                subtitle: 'معلومات عن التطبيق والإصدار',
                onTap: () {
                  _showAboutDialog();
                },
              ),
            ],
          ),

          const SizedBox(height: 32),
        ],
      ),
    ),
    );
  }

  // بناء بطاقة المستخدم
  Widget _buildUserProfileCard({
    required bool isAnonymous,
    required dynamic userModel,
    required VoidCallback onTap,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Colors.blue.shade50,
                  Colors.blue.shade100,
                ],
              ),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.blue.shade200,
                width: 1,
              ),
            ),
            child: Row(
              children: [
                // صورة المستخدم
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Colors.blue.shade400,
                        Colors.blue.shade600,
                      ],
                    ),
                    border: Border.all(
                      color: Colors.white,
                      width: 3,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.blue.shade200,
                        blurRadius: 8,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Icon(
                    isAnonymous ? Icons.person_outline : Icons.person,
                    color: Colors.white,
                    size: 30,
                  ),
                ),

                const SizedBox(width: 16),

                // معلومات المستخدم
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        isAnonymous
                          ? 'مستخدم ضيف'
                          : userModel?.fullName ?? 'المستخدم',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        isAnonymous
                          ? 'سجل دخولك لحفظ بياناتك'
                          : userModel?.email ?? '',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade600,
                        ),
                      ),
                      if (!isAnonymous && userModel?.university != null && userModel.university.isNotEmpty) ...[
                        const SizedBox(height: 2),
                        Text(
                          userModel.university,
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey.shade500,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),

                // سهم التنقل
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.8),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.arrow_forward_ios,
                    color: Colors.blue.shade700,
                    size: 16,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // بناء قسم في الإعدادات
  Widget _buildSettingsSection({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Icon(icon, color: Colors.blue.shade700, size: 24),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          const Divider(height: 1),
          ...children,
        ],
      ),
    );
  }

  // بناء عنصر في الإعدادات
  Widget _buildSettingsItem({
    required IconData icon,
    required String title,
    required String subtitle,
    Widget? trailing,
    Color? textColor,
    VoidCallback? onTap,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: textColor ?? Colors.grey.shade700,
        size: 24,
      ),
      title: Text(
        title,
        style: TextStyle(
          fontWeight: FontWeight.w600,
          color: textColor,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          color: Colors.grey.shade600,
          fontSize: 14,
        ),
      ),
      trailing: trailing ?? const Icon(Icons.arrow_forward_ios, size: 16),
      onTap: onTap,
    );
  }

  // التنقل إلى شاشة تعديل الملف الشخصي
  void _navigateToEditProfile(dynamic userModel) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => EditProfileScreen(userModel: userModel),
      ),
    );
  }

  // التنقل إلى شاشة تغيير كلمة المرور
  void _navigateToChangePassword() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const ChangePasswordScreen(),
      ),
    );
  }

  // التنقل إلى شاشة إعدادات الخصوصية
  void _navigateToPrivacySettings() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const PrivacySettingsScreen(),
      ),
    );
  }

  // عرض حوار يطلب تسجيل الدخول
  void _showLoginRequiredDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تسجيل الدخول مطلوب'),
        content: const Text(
          'يجب عليك تسجيل الدخول أولاً لتتمكن من تعديل ملفك الشخصي.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  // عرض رسالة "قريباً"
  void _showComingSoonSnackBar(String feature) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('ميزة $feature قيد التطوير وستكون متاحة قريباً'),
        backgroundColor: Colors.blue.shade600,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }

  // حوار تأكيد حذف الحساب
  void _showDeleteAccountDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف الحساب'),
        content: const Text(
          'هل أنت متأكد من حذف حسابك نهائياً؟\n\n'
          'سيتم حذف جميع بياناتك ومخططاتك الذهنية ولن يمكن استرجاعها.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _showComingSoonSnackBar('حذف الحساب');
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف نهائياً'),
          ),
        ],
      ),
    );
  }

  // حوار حول التطبيق
  void _showAboutDialog() {
    showAboutDialog(
      context: context,
      applicationName: 'مخطط ذهني للطلاب',
      applicationVersion: '1.0.0',
      applicationIcon: Icon(
        Icons.account_tree,
        size: 48,
        color: Colors.blue.shade700,
      ),
      children: [
        const Text(
          'تطبيق مخصص لمساعدة الطلاب في إنشاء ومشاركة المخططات الذهنية '
          'لتحسين عملية التعلم والدراسة.',
        ),
      ],
    );
  }

  // عرض حوار تأكيد تسجيل الخروج
  void _showSignOutDialog(AuthProvider authProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تسجيل الخروج'),
        content: const Text('هل أنت متأكد من تسجيل الخروج؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop(); // إغلاق الحوار

              // عرض شاشة التحميل الجميلة
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => LogoutLoadingScreen(
                    title: 'جاري تسجيل الخروج...',
                    subtitle: 'شكراً لاستخدام التطبيق',
                    duration: const Duration(seconds: 3),
                    gradientColors: [
                      Colors.blue.shade500,
                      Colors.blue.shade700,
                      Colors.blue.shade900,
                    ],
                    onComplete: () async {
                      try {
                        // تسجيل الخروج الفعلي
                        await authProvider.signOut();
                        if (context.mounted) {
                          Navigator.of(context).pushNamedAndRemoveUntil('/auth', (route) => false);
                        }
                      } catch (e) {
                        if (context.mounted) {
                          Navigator.of(context).pop(); // العودة للإعدادات
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('خطأ في تسجيل الخروج: $e'),
                              backgroundColor: Colors.red,
                            ),
                          );
                        }
                      }
                    },
                  ),
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('تسجيل الخروج'),
          ),
        ],
      ),
    );
  }
}
