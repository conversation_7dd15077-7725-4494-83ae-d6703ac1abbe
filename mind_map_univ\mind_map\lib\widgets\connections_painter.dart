import 'dart:math';
import 'package:flutter/material.dart';
import '../models/mind_map_connection.dart';
import '../models/mind_map_node.dart';

/// رسام الخطوط بين العقد
class ConnectionsPainter extends CustomPainter {
  final Map<String, MindMapConnection> connections;
  final Map<String, MindMapNode> nodes;
  final MindMapConnection? selectedConnection;

  ConnectionsPainter({
    required this.connections,
    required this.nodes,
    this.selectedConnection,
  });

  @override
  void paint(Canvas canvas, Size size) {
    for (final connection in connections.values) {
      if (!connection.isVisible) continue;

      final fromNode = nodes[connection.fromNodeId];
      final toNode = nodes[connection.toNodeId];

      if (fromNode == null || toNode == null) continue;

      _drawConnection(canvas, connection, fromNode, toNode);
    }
  }

  void _drawConnection(
    Canvas canvas,
    MindMapConnection connection,
    MindMapNode fromNode,
    MindMapNode toNode,
  ) {
    final isSelected = selectedConnection?.id == connection.id;
    
    // إعداد الفرشاة
    final paint = Paint()
      ..color = isSelected
          ? connection.color.withValues(alpha: 0.8)
          : connection.color.withValues(alpha: 0.7)
      ..strokeWidth = isSelected
          ? connection.thickness + 2
          : connection.thickness
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    // حساب نقاط الاتصال على حواف العقد
    final connectionPoints = _calculateConnectionPoints(fromNode, toNode);
    final startPoint = connectionPoints['start']!;
    final endPoint = connectionPoints['end']!;

    // رسم الخط حسب النوع
    switch (connection.type) {
      case ConnectionType.straight:
        _drawStraightLine(canvas, paint, startPoint, endPoint);
        break;
      case ConnectionType.curved:
        _drawCurvedLine(canvas, paint, startPoint, endPoint);
        break;
      case ConnectionType.stepped:
        _drawSteppedLine(canvas, paint, startPoint, endPoint);
        break;
      case ConnectionType.dashed:
        _drawDashedLine(canvas, paint, startPoint, endPoint);
        break;
    }

    // رسم السهم
    if (connection.arrowStyle != ArrowStyle.none) {
      _drawArrow(canvas, paint, startPoint, endPoint, connection.arrowStyle);
    }

    // رسم التسمية إذا وجدت
    if (connection.label.isNotEmpty) {
      _drawLabel(canvas, connection.label, startPoint, endPoint);
    }
  }

  Map<String, Offset> _calculateConnectionPoints(
    MindMapNode fromNode,
    MindMapNode toNode,
  ) {
    final fromCenter = fromNode.position;
    final toCenter = toNode.position;

    // حساب الاتجاه
    final direction = toCenter - fromCenter;
    final distance = direction.distance;
    final normalizedDirection = direction / distance;

    // حساب نقاط الاتصال على حواف العقد
    final fromRadius = sqrt(pow(fromNode.width / 2, 2) + pow(fromNode.height / 2, 2));
    final toRadius = sqrt(pow(toNode.width / 2, 2) + pow(toNode.height / 2, 2));

    final startPoint = fromCenter + normalizedDirection * (fromRadius * 0.7);
    final endPoint = toCenter - normalizedDirection * (toRadius * 0.7);

    return {
      'start': startPoint,
      'end': endPoint,
    };
  }

  void _drawStraightLine(Canvas canvas, Paint paint, Offset start, Offset end) {
    canvas.drawLine(start, end, paint);
  }

  void _drawCurvedLine(Canvas canvas, Paint paint, Offset start, Offset end) {
    final controlPoint1 = Offset(
      start.dx + (end.dx - start.dx) * 0.5,
      start.dy,
    );
    final controlPoint2 = Offset(
      start.dx + (end.dx - start.dx) * 0.5,
      end.dy,
    );

    final path = Path()
      ..moveTo(start.dx, start.dy)
      ..cubicTo(
        controlPoint1.dx, controlPoint1.dy,
        controlPoint2.dx, controlPoint2.dy,
        end.dx, end.dy,
      );

    canvas.drawPath(path, paint);
  }

  void _drawSteppedLine(Canvas canvas, Paint paint, Offset start, Offset end) {
    final midX = start.dx + (end.dx - start.dx) * 0.5;
    
    final path = Path()
      ..moveTo(start.dx, start.dy)
      ..lineTo(midX, start.dy)
      ..lineTo(midX, end.dy)
      ..lineTo(end.dx, end.dy);

    canvas.drawPath(path, paint);
  }

  void _drawDashedLine(Canvas canvas, Paint paint, Offset start, Offset end) {
    const dashWidth = 8.0;
    const dashSpace = 4.0;
    
    final direction = end - start;
    final distance = direction.distance;
    final normalizedDirection = direction / distance;

    double currentDistance = 0;
    bool isDash = true;

    while (currentDistance < distance) {
      final segmentLength = isDash ? dashWidth : dashSpace;
      final segmentEnd = min(currentDistance + segmentLength, distance);
      
      if (isDash) {
        final segmentStart = start + normalizedDirection * currentDistance;
        final segmentEndPoint = start + normalizedDirection * segmentEnd;
        canvas.drawLine(segmentStart, segmentEndPoint, paint);
      }
      
      currentDistance = segmentEnd;
      isDash = !isDash;
    }
  }

  void _drawArrow(
    Canvas canvas,
    Paint paint,
    Offset start,
    Offset end,
    ArrowStyle style,
  ) {
    final direction = end - start;
    final normalizedDirection = direction / direction.distance;
    
    const arrowLength = 12.0;
    const arrowAngle = pi / 6; // 30 degrees

    final arrowPoint1 = end - Offset(
      normalizedDirection.dx * arrowLength * cos(arrowAngle) - 
      normalizedDirection.dy * arrowLength * sin(arrowAngle),
      normalizedDirection.dy * arrowLength * cos(arrowAngle) + 
      normalizedDirection.dx * arrowLength * sin(arrowAngle),
    );

    final arrowPoint2 = end - Offset(
      normalizedDirection.dx * arrowLength * cos(-arrowAngle) - 
      normalizedDirection.dy * arrowLength * sin(-arrowAngle),
      normalizedDirection.dy * arrowLength * cos(-arrowAngle) + 
      normalizedDirection.dx * arrowLength * sin(-arrowAngle),
    );

    switch (style) {
      case ArrowStyle.simple:
        canvas.drawLine(end, arrowPoint1, paint);
        canvas.drawLine(end, arrowPoint2, paint);
        break;
      case ArrowStyle.filled:
        final arrowPath = Path()
          ..moveTo(end.dx, end.dy)
          ..lineTo(arrowPoint1.dx, arrowPoint1.dy)
          ..lineTo(arrowPoint2.dx, arrowPoint2.dy)
          ..close();
        canvas.drawPath(arrowPath, paint..style = PaintingStyle.fill);
        break;
      case ArrowStyle.double:
        // رسم سهم مزدوج
        canvas.drawLine(end, arrowPoint1, paint);
        canvas.drawLine(end, arrowPoint2, paint);
        
        final secondArrowPoint1 = end - normalizedDirection * arrowLength * 1.5 - Offset(
          normalizedDirection.dx * arrowLength * 0.5 * cos(arrowAngle) - 
          normalizedDirection.dy * arrowLength * 0.5 * sin(arrowAngle),
          normalizedDirection.dy * arrowLength * 0.5 * cos(arrowAngle) + 
          normalizedDirection.dx * arrowLength * 0.5 * sin(arrowAngle),
        );
        
        final secondArrowPoint2 = end - normalizedDirection * arrowLength * 1.5 - Offset(
          normalizedDirection.dx * arrowLength * 0.5 * cos(-arrowAngle) - 
          normalizedDirection.dy * arrowLength * 0.5 * sin(-arrowAngle),
          normalizedDirection.dy * arrowLength * 0.5 * cos(-arrowAngle) + 
          normalizedDirection.dx * arrowLength * 0.5 * sin(-arrowAngle),
        );
        
        final secondArrowBase = end - normalizedDirection * arrowLength * 1.5;
        canvas.drawLine(secondArrowBase, secondArrowPoint1, paint);
        canvas.drawLine(secondArrowBase, secondArrowPoint2, paint);
        break;
      case ArrowStyle.none:
        break;
    }
  }

  void _drawLabel(Canvas canvas, String label, Offset start, Offset end) {
    final midPoint = Offset(
      (start.dx + end.dx) / 2,
      (start.dy + end.dy) / 2,
    );

    final textPainter = TextPainter(
      text: TextSpan(
        text: label,
        style: const TextStyle(
          color: Colors.black87,
          fontSize: 12,
          fontWeight: FontWeight.w500,
          backgroundColor: Colors.white,
        ),
      ),
      textDirection: TextDirection.rtl,
    );

    textPainter.layout();
    
    // رسم خلفية للنص
    final rect = Rect.fromCenter(
      center: midPoint,
      width: textPainter.width + 8,
      height: textPainter.height + 4,
    );
    
    canvas.drawRRect(
      RRect.fromRectAndRadius(rect, const Radius.circular(4)),
      Paint()..color = Colors.white.withValues(alpha: 0.9),
    );

    textPainter.paint(
      canvas,
      midPoint - Offset(textPainter.width / 2, textPainter.height / 2),
    );
  }

  @override
  bool shouldRepaint(ConnectionsPainter oldDelegate) {
    return connections != oldDelegate.connections ||
           nodes != oldDelegate.nodes ||
           selectedConnection != oldDelegate.selectedConnection;
  }
}
