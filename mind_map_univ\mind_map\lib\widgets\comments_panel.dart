import 'package:flutter/material.dart';
import '../models/mind_map.dart';
import '../models/mind_map_comment.dart';
import 'add_comment_dialog.dart';
import 'edit_comment_dialog.dart';

class CommentsPanel extends StatefulWidget {
  final MindMap mindMap;
  final Function(MindMapComment) onCommentAdded;
  final Function(MindMapComment) onCommentUpdated;
  final Function(String) onCommentDeleted;
  final bool isReadOnly;

  const CommentsPanel({
    super.key,
    required this.mindMap,
    required this.onCommentAdded,
    required this.onCommentUpdated,
    required this.onCommentDeleted,
    this.isReadOnly = false,
  });

  @override
  State<CommentsPanel> createState() => _CommentsPanelState();
}

class _CommentsPanelState extends State<CommentsPanel> {
  CommentType? _selectedFilter;
  String? _selectedNodeFilter;

  @override
  Widget build(BuildContext context) {
    final comments = _getFilteredComments();

    return Container(
      width: 300,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          left: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: Column(
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surfaceContainerHighest,
              border: Border(
                bottom: BorderSide(
                  color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
                ),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.comment,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'التعليقات والملاحظات',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${widget.mindMap.commentCount}',
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.primary,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Filters
          Container(
            padding: const EdgeInsets.all(12),
            child: Column(
              children: [
                // Type filter
                DropdownButtonFormField<CommentType?>(
                  value: _selectedFilter,
                  decoration: const InputDecoration(
                    labelText: 'فلترة حسب النوع',
                    border: OutlineInputBorder(),
                    contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  ),
                  items: [
                    const DropdownMenuItem<CommentType?>(
                      value: null,
                      child: Text('جميع الأنواع'),
                    ),
                    ...CommentType.values.map((type) {
                      return DropdownMenuItem<CommentType?>(
                        value: type,
                        child: Row(
                          children: [
                            Text(type.icon),
                            const SizedBox(width: 8),
                            Text(type.displayName),
                          ],
                        ),
                      );
                    }),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _selectedFilter = value;
                    });
                  },
                ),

                const SizedBox(height: 8),

                // Node filter
                DropdownButtonFormField<String?>(
                  value: _selectedNodeFilter,
                  decoration: const InputDecoration(
                    labelText: 'فلترة حسب العقدة',
                    border: OutlineInputBorder(),
                    contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  ),
                  items: [
                    const DropdownMenuItem<String?>(
                      value: null,
                      child: Text('جميع العقد'),
                    ),
                    const DropdownMenuItem<String?>(
                      value: 'general',
                      child: Text('تعليقات عامة'),
                    ),
                    ...widget.mindMap.nodes.values.map((node) {
                      return DropdownMenuItem<String?>(
                        value: node.id,
                        child: Text(
                          node.title.length > 20
                              ? '${node.title.substring(0, 20)}...'
                              : node.title,
                        ),
                      );
                    }),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _selectedNodeFilter = value;
                    });
                  },
                ),
              ],
            ),
          ),

          // Add comment button (إخفاء في وضع القراءة فقط)
          if (!widget.isReadOnly)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 12),
              child: SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: _showAddCommentDialog,
                  icon: const Icon(Icons.add_comment),
                  label: const Text('إضافة تعليق'),
                ),
              ),
            ),

          const SizedBox(height: 8),

          // Comments list
          Expanded(
            child: comments.isEmpty
                ? _buildEmptyState()
                : ListView.builder(
                    padding: const EdgeInsets.symmetric(horizontal: 12),
                    itemCount: comments.length,
                    itemBuilder: (context, index) {
                      final comment = comments[index];
                      return _buildCommentCard(comment);
                    },
                  ),
          ),
        ],
      ),
    );
  }

  List<MindMapComment> _getFilteredComments() {
    var comments = widget.mindMap.getAllComments();

    // Filter by type
    if (_selectedFilter != null) {
      comments = comments.where((c) => c.type == _selectedFilter).toList();
    }

    // Filter by node
    if (_selectedNodeFilter != null) {
      if (_selectedNodeFilter == 'general') {
        comments = comments.where((c) => c.nodeId == null).toList();
      } else {
        comments = comments.where((c) => c.nodeId == _selectedNodeFilter).toList();
      }
    }

    // Sort by creation date (newest first)
    comments.sort((a, b) => b.createdAt.compareTo(a.createdAt));

    return comments;
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.comment_outlined,
            size: 64,
            color: Theme.of(context).colorScheme.outline,
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد تعليقات',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Theme.of(context).colorScheme.outline,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'ابدأ بإضافة تعليق أو ملاحظة',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.outline,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildCommentCard(MindMapComment comment) {
    final typeColor = Color(int.parse(comment.type.color.substring(1), radix: 16) + 0xFF000000);
    
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: typeColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: typeColor.withValues(alpha: 0.3)),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(comment.type.icon),
                      const SizedBox(width: 4),
                      Text(
                        comment.type.displayName,
                        style: TextStyle(
                          color: typeColor,
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
                const Spacer(),
                // إخفاء قائمة الخيارات في وضع القراءة فقط
                if (!widget.isReadOnly)
                  PopupMenuButton<String>(
                    onSelected: (value) => _handleCommentAction(value, comment),
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'edit',
                        child: ListTile(
                          leading: Icon(Icons.edit),
                          title: Text('تحرير'),
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'delete',
                        child: ListTile(
                          leading: Icon(Icons.delete, color: Colors.red),
                          title: Text('حذف', style: TextStyle(color: Colors.red)),
                        ),
                      ),
                    ],
                  ),
              ],
            ),

            const SizedBox(height: 8),

            // Content
            Text(
              comment.content,
              style: Theme.of(context).textTheme.bodyMedium,
            ),

            const SizedBox(height: 8),

            // Node attachment info
            if (comment.isAttachedToNode) ...[
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.05),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.2),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.account_tree,
                      size: 16,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'مرتبط بالعقدة:',
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Theme.of(context).colorScheme.primary,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const SizedBox(height: 2),
                          Text(
                            _getNodeName(comment.nodeId!),
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Theme.of(context).colorScheme.onSurface,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 8),
            ],

            // Footer
            Row(
              children: [
                Icon(
                  Icons.access_time,
                  size: 14,
                  color: Theme.of(context).colorScheme.outline,
                ),
                const SizedBox(width: 4),
                Text(
                  _formatDate(comment.createdAt),
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.outline,
                  ),
                ),
                const Spacer(),
                if (comment.updatedAt != comment.createdAt) ...[
                  Icon(
                    Icons.edit,
                    size: 12,
                    color: Theme.of(context).colorScheme.outline,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'محدث: ${_formatDate(comment.updatedAt)}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.outline,
                      fontSize: 10,
                    ),
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }

  String _getNodeName(String nodeId) {
    final node = widget.mindMap.nodes[nodeId];
    if (node == null) return 'عقدة محذوفة';

    // عرض اسم العقدة مع معلومات إضافية
    String nodeName = node.title.isEmpty ? 'عقدة بدون عنوان' : node.title;

    // إضافة معلومات عن العقد المرتبطة
    String additionalInfo = '';
    if (node.parentId != null) {
      final parentNode = widget.mindMap.nodes[node.parentId!];
      if (parentNode != null) {
        String parentName = parentNode.title.isEmpty ? 'عقدة بدون عنوان' : parentNode.title;
        if (parentName.length > 10) parentName = '${parentName.substring(0, 10)}...';
        additionalInfo = ' (فرع من: $parentName)';
      }
    } else if (node.childrenIds.isNotEmpty) {
      additionalInfo = ' (${node.childrenIds.length} فرع)';
    }

    String fullName = nodeName + additionalInfo;
    return fullName.length > 40 ? '${fullName.substring(0, 40)}...' : fullName;
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 0) {
      return 'منذ ${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inMinutes > 0) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else {
      return 'الآن';
    }
  }

  void _showAddCommentDialog() {
    showDialog(
      context: context,
      builder: (context) => AddCommentDialog(
        mindMap: widget.mindMap,
        onCommentAdded: widget.onCommentAdded,
      ),
    );
  }

  void _handleCommentAction(String action, MindMapComment comment) {
    switch (action) {
      case 'edit':
        _showEditCommentDialog(comment);
        break;
      case 'delete':
        _showDeleteConfirmation(comment);
        break;
    }
  }

  void _showEditCommentDialog(MindMapComment comment) {
    showDialog(
      context: context,
      builder: (context) => EditCommentDialog(
        comment: comment,
        mindMap: widget.mindMap,
        onCommentUpdated: widget.onCommentUpdated,
      ),
    );
  }

  void _showDeleteConfirmation(MindMapComment comment) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف التعليق'),
        content: const Text('هل أنت متأكد من حذف هذا التعليق؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              widget.onCommentDeleted(comment.id);
              Navigator.pop(context);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }
}
