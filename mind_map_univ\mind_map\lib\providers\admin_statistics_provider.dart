import 'package:flutter/foundation.dart';
import 'package:firebase_database/firebase_database.dart';
import '../models/admin_statistics.dart';
import '../models/user_model.dart';
import '../data/predefined_templates.dart';

class AdminStatisticsProvider with ChangeNotifier {
  final FirebaseDatabase _database = FirebaseDatabase.instance;
  
  AdminStatistics _statistics = AdminStatistics.empty();
  bool _isLoading = false;
  String? _error;

  AdminStatistics get statistics => _statistics;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // تحميل جميع الإحصائيات
  Future<void> loadStatistics() async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // تحميل البيانات بشكل متوازي
      final results = await Future.wait([
        _loadUserStatistics(),
        _loadMindMapStatistics(),
        _loadPostStatistics(),
        _loadEditRequestStatistics(),
        _loadTemplateStatistics(),
      ]);

      final userStats = Map<String, dynamic>.from(results[0] as Map);
      final mindMapStats = Map<String, dynamic>.from(results[1] as Map);
      final postStats = Map<String, dynamic>.from(results[2] as Map);
      final editRequestStats = Map<String, dynamic>.from(results[3] as Map);
      final templateStats = Map<String, dynamic>.from(results[4] as Map);

      // طباعة البيانات للتشخيص
      debugPrint('📊 إحصائيات المستخدمين: ${userStats['totalUsers']}');
      debugPrint('📊 إحصائيات المخططات الذهنية: ${mindMapStats['totalMindMaps']}');
      debugPrint('📊 إحصائيات المنشورات: ${postStats['totalPosts']}');

      _statistics = AdminStatistics(
        // إحصائيات المستخدمين
        totalUsers: userStats['totalUsers'] ?? 0,
        activeUsersToday: userStats['activeUsersToday'] ?? 0,
        activeUsersThisWeek: userStats['activeUsersThisWeek'] ?? 0,
        activeUsersThisMonth: userStats['activeUsersThisMonth'] ?? 0,
        newUsersToday: userStats['newUsersToday'] ?? 0,
        newUsersThisWeek: userStats['newUsersThisWeek'] ?? 0,
        newUsersThisMonth: userStats['newUsersThisMonth'] ?? 0,
        usersByUniversity: userStats['usersByUniversity'] ?? {},
        usersByMajor: userStats['usersByMajor'] ?? {},
        usersByAge: userStats['usersByAge'] ?? {},

        // إحصائيات المخططات الذهنية
        totalMindMaps: mindMapStats['totalMindMaps'] ?? 0,
        mindMapsCreatedToday: mindMapStats['mindMapsCreatedToday'] ?? 0,
        mindMapsCreatedThisWeek: mindMapStats['mindMapsCreatedThisWeek'] ?? 0,
        mindMapsCreatedThisMonth: mindMapStats['mindMapsCreatedThisMonth'] ?? 0,
        mindMapsBySubject: mindMapStats['mindMapsBySubject'] ?? {},
        favoriteMindMaps: mindMapStats['favoriteMindMaps'] ?? 0,
        publishedMindMaps: mindMapStats['publishedMindMaps'] ?? 0,
        templatedMindMaps: mindMapStats['templatedMindMaps'] ?? 0,

        // إحصائيات المنشورات
        totalPosts: postStats['totalPosts'] ?? 0,
        postsCreatedToday: postStats['postsCreatedToday'] ?? 0,
        postsCreatedThisWeek: postStats['postsCreatedThisWeek'] ?? 0,
        postsCreatedThisMonth: postStats['postsCreatedThisMonth'] ?? 0,
        totalComments: postStats['totalComments'] ?? 0,
        totalReactions: postStats['totalReactions'] ?? 0,
        postsBySubject: postStats['postsBySubject'] ?? {},
        
        // إحصائيات طلبات التعديل
        totalEditRequests: editRequestStats['totalEditRequests'],
        pendingEditRequests: editRequestStats['pendingEditRequests'],
        approvedEditRequests: editRequestStats['approvedEditRequests'],
        rejectedEditRequests: editRequestStats['rejectedEditRequests'],
        editRequestsToday: editRequestStats['editRequestsToday'],
        editRequestsThisWeek: editRequestStats['editRequestsThisWeek'],
        
        // إحصائيات القوالب
        totalTemplates: templateStats['totalTemplates'],
        customTemplates: templateStats['customTemplates'],
        predefinedTemplates: templateStats['predefinedTemplates'],
        templatesByCategory: templateStats['templatesByCategory'],
        
        // إحصائيات التفاعل
        totalFollowRelations: userStats['totalFollowRelations'],
        averageFollowersPerUser: userStats['averageFollowersPerUser'],
        averageFollowingPerUser: userStats['averageFollowingPerUser'],
        averagePostsPerUser: userStats['totalUsers'] > 0 
            ? postStats['totalPosts'] / userStats['totalUsers'] 
            : 0.0,
        averageMindMapsPerUser: userStats['totalUsers'] > 0 
            ? mindMapStats['totalMindMaps'] / userStats['totalUsers'] 
            : 0.0,
      );

    } catch (e) {
      _error = 'خطأ في تحميل الإحصائيات: $e';
      debugPrint('خطأ في تحميل الإحصائيات: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // تحميل إحصائيات المستخدمين
  Future<Map<String, dynamic>> _loadUserStatistics() async {
    final usersRef = _database.ref('users');
    final snapshot = await usersRef.get();
    
    if (!snapshot.exists) {
      return {
        'totalUsers': 0,
        'activeUsersToday': 0,
        'activeUsersThisWeek': 0,
        'activeUsersThisMonth': 0,
        'newUsersToday': 0,
        'newUsersThisWeek': 0,
        'newUsersThisMonth': 0,
        'usersByUniversity': <String, int>{},
        'usersByMajor': <String, int>{},
        'usersByAge': <String, int>{},
        'totalFollowRelations': 0,
        'averageFollowersPerUser': 0.0,
        'averageFollowingPerUser': 0.0,
      };
    }

    final users = <UserModel>[];
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final weekAgo = today.subtract(const Duration(days: 7));
    final monthAgo = DateTime(now.year, now.month - 1, now.day);

    int activeUsersToday = 0;
    int activeUsersThisWeek = 0;
    int activeUsersThisMonth = 0;
    int newUsersToday = 0;
    int newUsersThisWeek = 0;
    int newUsersThisMonth = 0;
    
    final usersByUniversity = <String, int>{};
    final usersByMajor = <String, int>{};
    final usersByAge = <String, int>{};
    int totalFollowRelations = 0;

    for (final child in snapshot.children) {
      try {
        final userData = Map<String, dynamic>.from(child.value as Map);
        final user = UserModel.fromMap(userData);
        users.add(user);

        // حساب المستخدمين النشطين
        if (user.lastActiveAt.isAfter(today)) {
          activeUsersToday++;
        }
        if (user.lastActiveAt.isAfter(weekAgo)) {
          activeUsersThisWeek++;
        }
        if (user.lastActiveAt.isAfter(monthAgo)) {
          activeUsersThisMonth++;
        }

        // حساب المستخدمين الجدد
        if (user.createdAt.isAfter(today)) {
          newUsersToday++;
        }
        if (user.createdAt.isAfter(weekAgo)) {
          newUsersThisWeek++;
        }
        if (user.createdAt.isAfter(monthAgo)) {
          newUsersThisMonth++;
        }

        // تجميع حسب الجامعة
        usersByUniversity[user.university] = 
            (usersByUniversity[user.university] ?? 0) + 1;

        // تجميع حسب التخصص
        usersByMajor[user.major] = 
            (usersByMajor[user.major] ?? 0) + 1;

        // تجميع حسب العمر
        final ageGroup = '${(user.age ~/ 5) * 5}-${(user.age ~/ 5) * 5 + 4}';
        usersByAge[ageGroup] = (usersByAge[ageGroup] ?? 0) + 1;

        // حساب علاقات المتابعة
        totalFollowRelations += user.followers.length;
      } catch (e) {
        debugPrint('خطأ في معالجة بيانات المستخدم: $e');
      }
    }

    final totalUsers = users.length;
    final averageFollowersPerUser = totalUsers > 0 
        ? totalFollowRelations / totalUsers 
        : 0.0;
    
    final totalFollowing = users.fold<int>(0, (sum, user) => sum + user.following.length);
    final averageFollowingPerUser = totalUsers > 0 
        ? totalFollowing / totalUsers 
        : 0.0;

    return {
      'totalUsers': totalUsers,
      'activeUsersToday': activeUsersToday,
      'activeUsersThisWeek': activeUsersThisWeek,
      'activeUsersThisMonth': activeUsersThisMonth,
      'newUsersToday': newUsersToday,
      'newUsersThisWeek': newUsersThisWeek,
      'newUsersThisMonth': newUsersThisMonth,
      'usersByUniversity': usersByUniversity,
      'usersByMajor': usersByMajor,
      'usersByAge': usersByAge,
      'totalFollowRelations': totalFollowRelations,
      'averageFollowersPerUser': averageFollowersPerUser,
      'averageFollowingPerUser': averageFollowingPerUser,
    };
  }

  // تحميل إحصائيات المخططات الذهنية
  Future<Map<String, dynamic>> _loadMindMapStatistics() async {
    debugPrint('🔍 بدء تحميل إحصائيات المخططات الذهنية من userProjects...');
    final userProjectsRef = _database.ref('userProjects');
    final snapshot = await userProjectsRef.get();

    if (!snapshot.exists) {
      return {
        'totalMindMaps': 0,
        'mindMapsCreatedToday': 0,
        'mindMapsCreatedThisWeek': 0,
        'mindMapsCreatedThisMonth': 0,
        'mindMapsBySubject': <String, int>{},
        'favoriteMindMaps': 0,
        'publishedMindMaps': 0,
        'templatedMindMaps': 0,
      };
    }

    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final weekAgo = today.subtract(const Duration(days: 7));
    final monthAgo = DateTime(now.year, now.month - 1, now.day);

    int totalMindMaps = 0;
    int mindMapsCreatedToday = 0;
    int mindMapsCreatedThisWeek = 0;
    int mindMapsCreatedThisMonth = 0;
    int favoriteMindMaps = 0;
    int publishedMindMaps = 0;
    int templatedMindMaps = 0;

    final mindMapsBySubject = <String, int>{};

    // معالجة بيانات userProjects
    for (final userChild in snapshot.children) {
      try {
        final userValue = userChild.value;

        if (userValue is Map) {
          final userDataMap = Map<String, dynamic>.from(userValue);

          // البحث عن مجلد mindMaps داخل بيانات المستخدم
          if (userDataMap.containsKey('mindMaps')) {
            final mindMapsData = userDataMap['mindMaps'];

            if (mindMapsData is Map) {
              final mindMapsMap = Map<String, dynamic>.from(mindMapsData);

              // معالجة كل مخطط ذهني
              for (final mindMapData in mindMapsMap.values) {
                if (mindMapData is Map) {
                  try {
                    final mindMapMap = Map<String, dynamic>.from(mindMapData);
                    totalMindMaps++;

                    // التحقق من وجود تاريخ الإنشاء
                    if (mindMapMap.containsKey('createdAt')) {
                      final createdAt = DateTime.parse(mindMapMap['createdAt']);

                      // حساب المخططات المنشأة حديثاً
                      if (createdAt.isAfter(today)) {
                        mindMapsCreatedToday++;
                      }
                      if (createdAt.isAfter(weekAgo)) {
                        mindMapsCreatedThisWeek++;
                      }
                      if (createdAt.isAfter(monthAgo)) {
                        mindMapsCreatedThisMonth++;
                      }
                    }

                    // تجميع حسب المادة
                    final subject = mindMapMap['subject']?.toString() ?? 'غير محدد';
                    mindMapsBySubject[subject] = (mindMapsBySubject[subject] ?? 0) + 1;

                    // حساب المفضلة
                    if (mindMapMap['isFavorite'] == true) {
                      favoriteMindMaps++;
                    }

                    // حساب المنشورة
                    if (mindMapMap['isPublished'] == true) {
                      publishedMindMaps++;
                    }

                    // حساب المنشأة من قوالب
                    if (mindMapMap['isFromTemplate'] == true) {
                      templatedMindMaps++;
                    }
                  } catch (e) {
                    debugPrint('خطأ في معالجة بيانات المخطط الذهني: $e');
                  }
                }
              }
            }
          }
        }
      } catch (e) {
        debugPrint('خطأ في معالجة بيانات المستخدم: $e');
      }
    }

    debugPrint('✅ تم تحميل إحصائيات المخططات الذهنية: $totalMindMaps مخطط');
    debugPrint('📊 المخططات حسب المادة: $mindMapsBySubject');

    return {
      'totalMindMaps': totalMindMaps,
      'mindMapsCreatedToday': mindMapsCreatedToday,
      'mindMapsCreatedThisWeek': mindMapsCreatedThisWeek,
      'mindMapsCreatedThisMonth': mindMapsCreatedThisMonth,
      'mindMapsBySubject': mindMapsBySubject,
      'favoriteMindMaps': favoriteMindMaps,
      'publishedMindMaps': publishedMindMaps,
      'templatedMindMaps': templatedMindMaps,
    };
  }

  // تحميل إحصائيات المنشورات
  Future<Map<String, dynamic>> _loadPostStatistics() async {
    final postsRef = _database.ref('posts');
    final snapshot = await postsRef.get();

    if (!snapshot.exists) {
      return {
        'totalPosts': 0,
        'postsCreatedToday': 0,
        'postsCreatedThisWeek': 0,
        'postsCreatedThisMonth': 0,
        'totalComments': 0,
        'totalReactions': 0,
        'postsBySubject': <String, int>{},
      };
    }

    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final weekAgo = today.subtract(const Duration(days: 7));
    final monthAgo = DateTime(now.year, now.month - 1, now.day);

    int totalPosts = 0;
    int postsCreatedToday = 0;
    int postsCreatedThisWeek = 0;
    int postsCreatedThisMonth = 0;
    int totalComments = 0;
    int totalReactions = 0;

    final postsBySubject = <String, int>{};

    for (final child in snapshot.children) {
      try {
        final postData = Map<String, dynamic>.from(child.value as Map);
        totalPosts++;

        final createdAt = DateTime.parse(postData['createdAt']);

        // حساب المنشورات المنشأة حديثاً
        if (createdAt.isAfter(today)) {
          postsCreatedToday++;
        }
        if (createdAt.isAfter(weekAgo)) {
          postsCreatedThisWeek++;
        }
        if (createdAt.isAfter(monthAgo)) {
          postsCreatedThisMonth++;
        }

        // تجميع حسب المادة
        final subject = postData['mindMapSubject']?.toString() ?? 'غير محدد';
        postsBySubject[subject] = (postsBySubject[subject] ?? 0) + 1;

        // حساب التعليقات
        final commentsData = postData['comments'];
        if (commentsData != null) {
          if (commentsData is List) {
            totalComments += commentsData.length;
          } else if (commentsData is Map) {
            totalComments += commentsData.length;
          }
        }

        // حساب ردود الفعل
        final reactionsData = postData['reactions'];
        if (reactionsData is Map) {
          final reactions = Map<String, dynamic>.from(reactionsData);
          totalReactions += (reactions['support'] is num) ? (reactions['support'] as num).toInt() : 0;
          totalReactions += (reactions['like'] is num) ? (reactions['like'] as num).toInt() : 0;
          totalReactions += (reactions['excellent'] is num) ? (reactions['excellent'] as num).toInt() : 0;
          totalReactions += (reactions['good'] is num) ? (reactions['good'] as num).toInt() : 0;
          totalReactions += (reactions['veryGood'] is num) ? (reactions['veryGood'] as num).toInt() : 0;
        }
      } catch (e) {
        debugPrint('خطأ في معالجة بيانات المنشور: $e');
      }
    }

    return {
      'totalPosts': totalPosts,
      'postsCreatedToday': postsCreatedToday,
      'postsCreatedThisWeek': postsCreatedThisWeek,
      'postsCreatedThisMonth': postsCreatedThisMonth,
      'totalComments': totalComments,
      'totalReactions': totalReactions,
      'postsBySubject': postsBySubject,
    };
  }

  // تحميل إحصائيات طلبات التعديل
  Future<Map<String, dynamic>> _loadEditRequestStatistics() async {
    final editRequestsRef = _database.ref('editRequests');
    final snapshot = await editRequestsRef.get();

    if (!snapshot.exists) {
      return {
        'totalEditRequests': 0,
        'pendingEditRequests': 0,
        'approvedEditRequests': 0,
        'rejectedEditRequests': 0,
        'editRequestsToday': 0,
        'editRequestsThisWeek': 0,
      };
    }

    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final weekAgo = today.subtract(const Duration(days: 7));

    int totalEditRequests = 0;
    int pendingEditRequests = 0;
    int approvedEditRequests = 0;
    int rejectedEditRequests = 0;
    int editRequestsToday = 0;
    int editRequestsThisWeek = 0;

    for (final child in snapshot.children) {
      try {
        final requestData = Map<String, dynamic>.from(child.value as Map);
        totalEditRequests++;

        final createdAt = DateTime.parse(requestData['createdAt']);

        // حساب الطلبات المنشأة حديثاً
        if (createdAt.isAfter(today)) {
          editRequestsToday++;
        }
        if (createdAt.isAfter(weekAgo)) {
          editRequestsThisWeek++;
        }

        // حساب حسب الحالة
        final status = requestData['status']?.toString() ?? 'pending';
        switch (status) {
          case 'pending':
            pendingEditRequests++;
            break;
          case 'approved':
            approvedEditRequests++;
            break;
          case 'rejected':
            rejectedEditRequests++;
            break;
        }
      } catch (e) {
        debugPrint('خطأ في معالجة بيانات طلب التعديل: $e');
      }
    }

    return {
      'totalEditRequests': totalEditRequests,
      'pendingEditRequests': pendingEditRequests,
      'approvedEditRequests': approvedEditRequests,
      'rejectedEditRequests': rejectedEditRequests,
      'editRequestsToday': editRequestsToday,
      'editRequestsThisWeek': editRequestsThisWeek,
    };
  }

  // تحميل إحصائيات القوالب
  Future<Map<String, dynamic>> _loadTemplateStatistics() async {
    // القوالب المحددة مسبقاً
    final predefinedTemplates = PredefinedTemplates.getAllTemplates();
    final templatesByCategory = <String, int>{};

    for (final template in predefinedTemplates) {
      final categoryName = template.category.name;
      templatesByCategory[categoryName] = (templatesByCategory[categoryName] ?? 0) + 1;
    }

    // القوالب المخصصة (يمكن إضافة منطق لتحميلها من Firebase إذا كانت متوفرة)
    int customTemplates = 0;

    return {
      'totalTemplates': predefinedTemplates.length + customTemplates,
      'customTemplates': customTemplates,
      'predefinedTemplates': predefinedTemplates.length,
      'templatesByCategory': templatesByCategory,
    };
  }

  // تحديث الإحصائيات
  Future<void> refreshStatistics() async {
    await loadStatistics();
  }



  // مسح الخطأ
  void clearError() {
    _error = null;
    notifyListeners();
  }
}
