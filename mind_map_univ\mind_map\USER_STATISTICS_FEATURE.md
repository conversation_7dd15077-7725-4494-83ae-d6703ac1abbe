# ميزة إحصائيات المستخدمين التفصيلية

## 📊 نظرة عامة

تم إضافة ميزة جديدة لعرض إحصائيات تفصيلية لكل مستخدم في لوحة الإدارة. عند الضغط على أي مستخدم في شاشة إدارة المستخدمين، ستظهر صفحة تحتوي على إحصائيات شاملة عن نشاط المستخدم.

## 🎯 الميزات المضافة

### 1. **نموذج إحصائيات المستخدم** (`UserStatistics`)
- إحصائيات المشاريع (إجمالي، مفضلة، منشورة، من قوالب)
- إحصائيات المنشورات (إجمالي، إعجابات، ردود فعل، تعليقات)
- إحصائيات التفاعل (تعليقات وردود فعل مقدمة)
- إحصائيات الوقت والنشاط (وقت إجمالي، جلسات، معدل نشاط)
- إحصائيات المتابعة (متابعين، متابَعين)

### 2. **مقدم خدمة الإحصائيات** (`UserStatisticsProvider`)
- تحميل وحساب الإحصائيات من Firebase
- حساب إحصائيات المخططات الذهنية
- حساب إحصائيات المنشورات والتفاعلات
- حساب إحصائيات الوقت والنشاط

### 3. **شاشة تفاصيل المستخدم** (`AdminUserDetailsScreen`)
- عرض معلومات المستخدم الأساسية
- بطاقات منظمة للإحصائيات المختلفة
- تصميم جذاب ومنظم
- معالجة حالات الخطأ والتحميل

### 4. **مكون ملخص الإحصائيات** (`UserStatsSummaryCard`)
- بطاقة قابلة لإعادة الاستخدام
- عرض مختصر أو مفصل للإحصائيات
- تصميم متجاوب

## 📁 الملفات المضافة/المحدثة

### ملفات جديدة:
- `lib/models/user_statistics.dart` - نموذج إحصائيات المستخدم
- `lib/providers/user_statistics_provider.dart` - مقدم خدمة الإحصائيات
- `lib/screens/admin_user_details_screen.dart` - شاشة تفاصيل المستخدم
- `lib/widgets/user_stats_summary_card.dart` - مكون ملخص الإحصائيات

### ملفات محدثة:
- `lib/main.dart` - إضافة مقدم خدمة الإحصائيات
- `lib/screens/admin_users_management_screen.dart` - إضافة إمكانية الضغط على المستخدم

## 🔧 كيفية الاستخدام

1. **الوصول للميزة:**
   - اذهب إلى لوحة الإدارة
   - اختر "إدارة المستخدمين"
   - اضغط على أي مستخدم لعرض تفاصيله

2. **الإحصائيات المعروضة:**
   - **المعلومات الأساسية:** الاسم، البريد، الجامعة، التخصص، العمر
   - **إحصائيات المشاريع:** عدد المخططات الذهنية وتصنيفاتها
   - **إحصائيات المنشورات:** المنشورات والتفاعلات المستلمة
   - **إحصائيات التفاعل:** التعليقات وردود الفعل المقدمة
   - **إحصائيات الوقت:** الوقت المقضي ومعدل النشاط
   - **إحصائيات المتابعة:** المتابعين والمتابَعين

## 📊 الإحصائيات المحسوبة

### إحصائيات المشاريع:
- إجمالي المخططات الذهنية
- المخططات المفضلة
- المخططات المنشورة
- المخططات المنشأة من قوالب
- توزيع المخططات حسب المادة

### إحصائيات المنشورات:
- إجمالي المنشورات
- إجمالي الإعجابات المستلمة
- إجمالي ردود الفعل المستلمة
- إجمالي التعليقات المستلمة
- معدل التفاعل (تفاعل/منشور)

### إحصائيات التفاعل:
- التعليقات المقدمة على منشورات الآخرين
- ردود الفعل المقدمة على منشورات الآخرين

### إحصائيات الوقت والنشاط:
- إجمالي الوقت المقضي (تقديري)
- متوسط الوقت اليومي
- عدد الجلسات (تقديري)
- متوسط مدة الجلسة
- عدد الأيام النشطة
- معدل النشاط اليومي

### إحصائيات المتابعة:
- عدد المتابعين
- عدد المتابَعين

## 🎨 التصميم والواجهة

- **تصميم متجاوب:** يعمل على جميع أحجام الشاشات
- **بطاقات منظمة:** كل نوع إحصائيات في بطاقة منفصلة
- **ألوان مميزة:** كل إحصائية لها لون مميز
- **أيقونات واضحة:** أيقونات تعبر عن نوع الإحصائية
- **معالجة الأخطاء:** رسائل واضحة عند حدوث أخطاء

## 🔮 التطويرات المستقبلية

1. **تتبع الوقت الفعلي:**
   - إضافة نظام تتبع دقيق للوقت المقضي
   - تسجيل أوقات الدخول والخروج
   - حساب الوقت الفعلي لكل جلسة

2. **إحصائيات متقدمة:**
   - رسوم بيانية للنشاط عبر الزمن
   - مقارنة الأداء مع المستخدمين الآخرين
   - تحليل أنماط الاستخدام

3. **تصدير الإحصائيات:**
   - تصدير إلى PDF
   - تصدير إلى Excel
   - إرسال تقارير دورية

4. **إشعارات الإحصائيات:**
   - إشعارات للمدراء عند تحقيق إنجازات
   - تقارير أسبوعية/شهرية
   - تنبيهات عند انخفاض النشاط

## 🛠️ ملاحظات تقنية

- **قاعدة البيانات:** يتم حساب الإحصائيات من Firebase Realtime Database
- **الأداء:** الحسابات تتم عند الطلب لضمان البيانات الحديثة
- **التخزين المؤقت:** يمكن إضافة تخزين مؤقت لتحسين الأداء
- **الأمان:** الوصول محدود للمدراء فقط

## 🐛 معالجة الأخطاء

- رسائل خطأ واضحة عند فشل تحميل البيانات
- إمكانية إعادة المحاولة
- عرض رسائل تحميل أثناء جلب البيانات
- التعامل مع البيانات المفقودة أو التالفة

---

**تم تطوير هذه الميزة لتوفير رؤية شاملة عن نشاط المستخدمين ومساعدة المدراء في اتخاذ قرارات مدروسة حول إدارة المنصة.**
