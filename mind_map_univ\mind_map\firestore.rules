rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // قواعد للمستخدمين - يمكن للمستخدم قراءة وكتابة بياناته فقط
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // قواعد للمخططات الذهنية - يمكن للمستخدم قراءة وكتابة مخططاته فقط
    match /mindMaps/{mindMapId} {
      allow read, write: if request.auth != null && request.auth.uid == resource.data.userId;
      allow create: if request.auth != null && request.auth.uid == request.resource.data.userId;
    }
    
    // قواعد للمواد الدراسية - يمكن للمستخدم قراءة وكتابة مواده فقط
    match /subjects/{subjectId} {
      allow read, write: if request.auth != null && request.auth.uid == resource.data.userId;
      allow create: if request.auth != null && request.auth.uid == request.resource.data.userId;
    }
    
    // قواعد للتعليقات - يمكن للمستخدم قراءة وكتابة تعليقاته فقط
    match /comments/{commentId} {
      allow read, write: if request.auth != null && request.auth.uid == resource.data.userId;
      allow create: if request.auth != null && request.auth.uid == request.resource.data.userId;
    }
    
    // قواعد للقوالب - يمكن للجميع قراءة القوالب العامة
    match /templates/{templateId} {
      allow read: if true; // القوالب متاحة للجميع للقراءة
      allow write: if request.auth != null && request.auth.uid == resource.data.createdBy;
      allow create: if request.auth != null && request.auth.uid == request.resource.data.createdBy;
    }
    
    // قواعد للقوالب المخصصة للمستخدمين
    match /userTemplates/{userId}/templates/{templateId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // منع الوصول لأي مجموعات أخرى
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
