import 'package:uuid/uuid.dart';
import 'package:flutter/material.dart';

class MindMapComment {
  final String id;
  String content;
  DateTime createdAt;
  DateTime updatedAt;
  String? nodeId; // إذا كان التعليق مرتبط بعقدة معينة
  double? positionX; // موقع التعليق على الشاشة
  double? positionY;
  CommentType type;
  String? color; // لون التعليق (اختياري)

  MindMapComment({
    String? id,
    required this.content,
    DateTime? createdAt,
    DateTime? updatedAt,
    this.nodeId,
    this.positionX,
    this.positionY,
    this.type = CommentType.general,
    this.color,
  }) : id = id ?? const Uuid().v4(),
       createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  // إنشاء نسخة من التعليق مع تعديل بعض الخصائص
  MindMapComment copyWith({
    String? content,
    String? nodeId,
    double? positionX,
    double? positionY,
    CommentType? type,
    String? color,
  }) {
    return MindMapComment(
      id: id,
      content: content ?? this.content,
      createdAt: createdAt,
      updatedAt: DateTime.now(),
      nodeId: nodeId ?? this.nodeId,
      positionX: positionX ?? this.positionX,
      positionY: positionY ?? this.positionY,
      type: type ?? this.type,
      color: color ?? this.color,
    );
  }

  // Getter للموقع كـ Offset
  Offset? get position {
    if (positionX != null && positionY != null) {
      return Offset(positionX!, positionY!);
    }
    return null;
  }

  // تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'content': content,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'nodeId': nodeId,
      'positionX': positionX,
      'positionY': positionY,
      'type': type.toString(),
      'color': color,
    };
  }

  // إنشاء من JSON
  factory MindMapComment.fromJson(Map<String, dynamic> json) {
    return MindMapComment(
      id: json['id'] ?? '',
      content: json['content'] ?? '',
      createdAt: json['createdAt'] != null
          ? DateTime.parse(json['createdAt'])
          : DateTime.now(),
      updatedAt: json['updatedAt'] != null
          ? DateTime.parse(json['updatedAt'])
          : DateTime.now(),
      nodeId: json['nodeId'],
      positionX: json['positionX']?.toDouble(),
      positionY: json['positionY']?.toDouble(),
      type: CommentType.values.firstWhere(
        (e) => e.toString() == json['type'],
        orElse: () => CommentType.general,
      ),
      color: json['color'],
    );
  }

  // تحديث محتوى التعليق
  void updateContent(String newContent) {
    content = newContent;
    updatedAt = DateTime.now();
  }

  // تحديث موقع التعليق
  void updatePosition(double x, double y) {
    positionX = x;
    positionY = y;
    updatedAt = DateTime.now();
  }

  // تحديث نوع التعليق
  void updateType(CommentType newType) {
    type = newType;
    updatedAt = DateTime.now();
  }

  // تحديث لون التعليق
  void updateColor(String? newColor) {
    color = newColor;
    updatedAt = DateTime.now();
  }

  // ربط التعليق بعقدة
  void attachToNode(String nodeId) {
    this.nodeId = nodeId;
    updatedAt = DateTime.now();
  }

  // إلغاء ربط التعليق من العقدة
  void detachFromNode() {
    nodeId = null;
    updatedAt = DateTime.now();
  }

  // التحقق من ارتباط التعليق بعقدة
  bool get isAttachedToNode => nodeId != null;

  // التحقق من وجود موقع للتعليق
  bool get hasPosition => positionX != null && positionY != null;

  @override
  String toString() {
    return 'MindMapComment(id: $id, content: ${content.length > 20 ? content.substring(0, 20) + '...' : content}, type: $type)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MindMapComment && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

// أنواع التعليقات
enum CommentType {
  general,     // تعليق عام
  note,        // ملاحظة
  reminder,    // تذكير
  question,    // سؤال
  important,   // مهم
  idea,        // فكرة
  todo,        // مهمة
}

// امتداد لتحويل نوع التعليق إلى نص عربي
extension CommentTypeExtension on CommentType {
  String get displayName {
    switch (this) {
      case CommentType.general:
        return 'عام';
      case CommentType.note:
        return 'ملاحظة';
      case CommentType.reminder:
        return 'تذكير';
      case CommentType.question:
        return 'سؤال';
      case CommentType.important:
        return 'مهم';
      case CommentType.idea:
        return 'فكرة';
      case CommentType.todo:
        return 'مهمة';
    }
  }

  String get icon {
    switch (this) {
      case CommentType.general:
        return '💬';
      case CommentType.note:
        return '📝';
      case CommentType.reminder:
        return '⏰';
      case CommentType.question:
        return '❓';
      case CommentType.important:
        return '⚠️';
      case CommentType.idea:
        return '💡';
      case CommentType.todo:
        return '✅';
    }
  }

  String get color {
    switch (this) {
      case CommentType.general:
        return '#2196F3'; // أزرق
      case CommentType.note:
        return '#4CAF50'; // أخضر
      case CommentType.reminder:
        return '#FF9800'; // برتقالي
      case CommentType.question:
        return '#9C27B0'; // بنفسجي
      case CommentType.important:
        return '#F44336'; // أحمر
      case CommentType.idea:
        return '#FFEB3B'; // أصفر
      case CommentType.todo:
        return '#00BCD4'; // سماوي
    }
  }
}
