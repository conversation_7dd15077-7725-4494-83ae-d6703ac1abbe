import 'package:flutter/material.dart';
import '../models/mind_map_template.dart';
import '../models/mind_map.dart';

class SaveAsTemplateDialog extends StatefulWidget {
  final MindMap mindMap;

  const SaveAsTemplateDialog({
    super.key,
    required this.mindMap,
  });

  @override
  State<SaveAsTemplateDialog> createState() => _SaveAsTemplateDialogState();
}

class _SaveAsTemplateDialogState extends State<SaveAsTemplateDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  TemplateCategory _selectedCategory = TemplateCategory.general;

  @override
  void initState() {
    super.initState();
    // تعبئة القيم الافتراضية من المخطط الحالي
    _nameController.text = widget.mindMap.title;
    _descriptionController.text = widget.mindMap.description;
    
    // محاولة تخمين الفئة من المادة الدراسية
    _selectedCategory = _guessCategory(widget.mindMap.subject);
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  TemplateCategory _guessCategory(String subject) {
    final subjectLower = subject.toLowerCase();
    
    if (subjectLower.contains('رياضيات') || subjectLower.contains('math')) {
      return TemplateCategory.mathematics;
    } else if (subjectLower.contains('فيزياء') || subjectLower.contains('physics')) {
      return TemplateCategory.physics;
    } else if (subjectLower.contains('كيمياء') || subjectLower.contains('chemistry')) {
      return TemplateCategory.chemistry;
    } else if (subjectLower.contains('تاريخ') || subjectLower.contains('history')) {
      return TemplateCategory.history;
    } else if (subjectLower.contains('عربية') || subjectLower.contains('arabic')) {
      return TemplateCategory.arabic;
    } else if (subjectLower.contains('أحياء') || subjectLower.contains('biology')) {
      return TemplateCategory.biology;
    } else if (subjectLower.contains('جغرافيا') || subjectLower.contains('geography')) {
      return TemplateCategory.geography;
    } else if (subjectLower.contains('أدب') || subjectLower.contains('literature')) {
      return TemplateCategory.literature;
    } else if (subjectLower.contains('فلسفة') || subjectLower.contains('philosophy')) {
      return TemplateCategory.philosophy;
    } else if (subjectLower.contains('حاسوب') || subjectLower.contains('computer')) {
      return TemplateCategory.computer;
    }
    
    return TemplateCategory.general;
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Row(
        children: [
          Icon(Icons.save_as, color: Theme.of(context).primaryColor),
          const SizedBox(width: 8),
          const Text('حفظ كقالب مخصص'),
        ],
      ),
      content: SizedBox(
        width: double.maxFinite,
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // معلومات المخطط الحالي
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.blue.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.blue.withOpacity(0.3)),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'المخطط الحالي:',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.blue[700],
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text('العنوان: ${widget.mindMap.title}'),
                      Text('عدد العقد: ${widget.mindMap.nodeCount}'),
                      Text('عدد الروابط: ${widget.mindMap.connectionCount}'),
                    ],
                  ),
                ),
                
                const SizedBox(height: 16),
                
                // اسم القالب
                TextFormField(
                  controller: _nameController,
                  decoration: const InputDecoration(
                    labelText: 'اسم القالب *',
                    hintText: 'أدخل اسم القالب',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.title),
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'يرجى إدخال اسم القالب';
                    }
                    return null;
                  },
                ),
                
                const SizedBox(height: 16),
                
                // وصف القالب
                TextFormField(
                  controller: _descriptionController,
                  decoration: const InputDecoration(
                    labelText: 'وصف القالب *',
                    hintText: 'وصف مختصر لاستخدام القالب',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.description),
                  ),
                  maxLines: 3,
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'يرجى إدخال وصف القالب';
                    }
                    return null;
                  },
                ),
                
                const SizedBox(height: 16),
                
                // فئة القالب
                DropdownButtonFormField<TemplateCategory>(
                  value: _selectedCategory,
                  decoration: const InputDecoration(
                    labelText: 'فئة القالب',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.category),
                  ),
                  items: TemplateCategory.values.map((category) {
                    return DropdownMenuItem(
                      value: category,
                      child: Row(
                        children: [
                          Icon(
                            category.icon,
                            color: category.color,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Text(category.displayName),
                        ],
                      ),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        _selectedCategory = value;
                      });
                    }
                  },
                ),
                
                const SizedBox(height: 16),
                
                // ملاحظة
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.orange.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.orange.withOpacity(0.3)),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.info, color: Colors.orange[700], size: 20),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'سيتم حفظ القالب مع جميع العقد والروابط الحالية. يمكن استخدامه لإنشاء مخططات جديدة.',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.orange[700],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: _saveTemplate,
          style: ElevatedButton.styleFrom(
            backgroundColor: _selectedCategory.color,
            foregroundColor: Colors.white,
          ),
          child: const Text('حفظ القالب'),
        ),
      ],
    );
  }

  void _saveTemplate() {
    if (_formKey.currentState!.validate()) {
      // إنشاء القالب من المخطط الحالي
      final template = MindMapTemplate.fromMindMap(
        widget.mindMap,
        templateName: _nameController.text.trim(),
        templateDescription: _descriptionController.text.trim(),
        category: _selectedCategory,
      );
      
      Navigator.of(context).pop(template);
    }
  }
}
