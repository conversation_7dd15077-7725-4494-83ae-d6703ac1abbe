class UserStatistics {
  final String userId;
  final String userName;
  final String userEmail;
  
  // إحصائيات المشاريع
  final int totalMindMaps;
  final int favoriteMindMaps;
  final int publishedMindMaps;
  final int templatedMindMaps;
  final Map<String, int> mindMapsBySubject;
  
  // إحصائيات المنشورات
  final int totalPosts;
  final int totalLikes;
  final int totalReactions;
  final int totalComments;
  final int totalShares;
  
  // إحصائيات التفاعل
  final int totalCommentsGiven;
  final int totalReactionsGiven;
  final int totalFollowers;
  final int totalFollowing;
  
  // إحصائيات الوقت
  final Duration totalTimeSpent;
  final DateTime firstLoginDate;
  final DateTime lastActiveDate;
  final int totalSessions;
  final Duration averageSessionDuration;
  
  // إحصائيات النشاط
  final int activeDaysCount;
  final Map<String, int> activityByDay; // نشاط حسب اليوم
  final Map<String, int> activityByMonth; // نشاط حسب الشهر
  
  UserStatistics({
    required this.userId,
    required this.userName,
    required this.userEmail,
    this.totalMindMaps = 0,
    this.favoriteMindMaps = 0,
    this.publishedMindMaps = 0,
    this.templatedMindMaps = 0,
    Map<String, int>? mindMapsBySubject,
    this.totalPosts = 0,
    this.totalLikes = 0,
    this.totalReactions = 0,
    this.totalComments = 0,
    this.totalShares = 0,
    this.totalCommentsGiven = 0,
    this.totalReactionsGiven = 0,
    this.totalFollowers = 0,
    this.totalFollowing = 0,
    this.totalTimeSpent = Duration.zero,
    DateTime? firstLoginDate,
    DateTime? lastActiveDate,
    this.totalSessions = 0,
    this.averageSessionDuration = Duration.zero,
    this.activeDaysCount = 0,
    Map<String, int>? activityByDay,
    Map<String, int>? activityByMonth,
  }) : mindMapsBySubject = mindMapsBySubject ?? {},
       firstLoginDate = firstLoginDate ?? DateTime.now(),
       lastActiveDate = lastActiveDate ?? DateTime.now(),
       activityByDay = activityByDay ?? {},
       activityByMonth = activityByMonth ?? {};

  // حساب معدل النشاط اليومي
  double get dailyActivityRate {
    if (activeDaysCount == 0) return 0.0;
    final daysSinceFirstLogin = DateTime.now().difference(firstLoginDate).inDays + 1;
    return (activeDaysCount / daysSinceFirstLogin) * 100;
  }

  // حساب معدل التفاعل
  double get engagementRate {
    if (totalPosts == 0) return 0.0;
    final totalEngagements = totalLikes + totalReactions + totalComments;
    return (totalEngagements / totalPosts);
  }

  // حساب متوسط الوقت اليومي
  Duration get averageDailyTime {
    if (activeDaysCount == 0) return Duration.zero;
    return Duration(minutes: totalTimeSpent.inMinutes ~/ activeDaysCount);
  }

  // تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'userName': userName,
      'userEmail': userEmail,
      'totalMindMaps': totalMindMaps,
      'favoriteMindMaps': favoriteMindMaps,
      'publishedMindMaps': publishedMindMaps,
      'templatedMindMaps': templatedMindMaps,
      'mindMapsBySubject': mindMapsBySubject,
      'totalPosts': totalPosts,
      'totalLikes': totalLikes,
      'totalReactions': totalReactions,
      'totalComments': totalComments,
      'totalShares': totalShares,
      'totalCommentsGiven': totalCommentsGiven,
      'totalReactionsGiven': totalReactionsGiven,
      'totalFollowers': totalFollowers,
      'totalFollowing': totalFollowing,
      'totalTimeSpent': totalTimeSpent.inMinutes,
      'firstLoginDate': firstLoginDate.toIso8601String(),
      'lastActiveDate': lastActiveDate.toIso8601String(),
      'totalSessions': totalSessions,
      'averageSessionDuration': averageSessionDuration.inMinutes,
      'activeDaysCount': activeDaysCount,
      'activityByDay': activityByDay,
      'activityByMonth': activityByMonth,
    };
  }

  // إنشاء من JSON
  factory UserStatistics.fromJson(Map<String, dynamic> json) {
    return UserStatistics(
      userId: json['userId'] ?? '',
      userName: json['userName'] ?? '',
      userEmail: json['userEmail'] ?? '',
      totalMindMaps: json['totalMindMaps'] ?? 0,
      favoriteMindMaps: json['favoriteMindMaps'] ?? 0,
      publishedMindMaps: json['publishedMindMaps'] ?? 0,
      templatedMindMaps: json['templatedMindMaps'] ?? 0,
      mindMapsBySubject: Map<String, int>.from(json['mindMapsBySubject'] ?? {}),
      totalPosts: json['totalPosts'] ?? 0,
      totalLikes: json['totalLikes'] ?? 0,
      totalReactions: json['totalReactions'] ?? 0,
      totalComments: json['totalComments'] ?? 0,
      totalShares: json['totalShares'] ?? 0,
      totalCommentsGiven: json['totalCommentsGiven'] ?? 0,
      totalReactionsGiven: json['totalReactionsGiven'] ?? 0,
      totalFollowers: json['totalFollowers'] ?? 0,
      totalFollowing: json['totalFollowing'] ?? 0,
      totalTimeSpent: Duration(minutes: json['totalTimeSpent'] ?? 0),
      firstLoginDate: json['firstLoginDate'] != null 
          ? DateTime.parse(json['firstLoginDate'])
          : DateTime.now(),
      lastActiveDate: json['lastActiveDate'] != null 
          ? DateTime.parse(json['lastActiveDate'])
          : DateTime.now(),
      totalSessions: json['totalSessions'] ?? 0,
      averageSessionDuration: Duration(minutes: json['averageSessionDuration'] ?? 0),
      activeDaysCount: json['activeDaysCount'] ?? 0,
      activityByDay: Map<String, int>.from(json['activityByDay'] ?? {}),
      activityByMonth: Map<String, int>.from(json['activityByMonth'] ?? {}),
    );
  }

  // نسخ مع تعديل
  UserStatistics copyWith({
    String? userId,
    String? userName,
    String? userEmail,
    int? totalMindMaps,
    int? favoriteMindMaps,
    int? publishedMindMaps,
    int? templatedMindMaps,
    Map<String, int>? mindMapsBySubject,
    int? totalPosts,
    int? totalLikes,
    int? totalReactions,
    int? totalComments,
    int? totalShares,
    int? totalCommentsGiven,
    int? totalReactionsGiven,
    int? totalFollowers,
    int? totalFollowing,
    Duration? totalTimeSpent,
    DateTime? firstLoginDate,
    DateTime? lastActiveDate,
    int? totalSessions,
    Duration? averageSessionDuration,
    int? activeDaysCount,
    Map<String, int>? activityByDay,
    Map<String, int>? activityByMonth,
  }) {
    return UserStatistics(
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      userEmail: userEmail ?? this.userEmail,
      totalMindMaps: totalMindMaps ?? this.totalMindMaps,
      favoriteMindMaps: favoriteMindMaps ?? this.favoriteMindMaps,
      publishedMindMaps: publishedMindMaps ?? this.publishedMindMaps,
      templatedMindMaps: templatedMindMaps ?? this.templatedMindMaps,
      mindMapsBySubject: mindMapsBySubject ?? Map.from(this.mindMapsBySubject),
      totalPosts: totalPosts ?? this.totalPosts,
      totalLikes: totalLikes ?? this.totalLikes,
      totalReactions: totalReactions ?? this.totalReactions,
      totalComments: totalComments ?? this.totalComments,
      totalShares: totalShares ?? this.totalShares,
      totalCommentsGiven: totalCommentsGiven ?? this.totalCommentsGiven,
      totalReactionsGiven: totalReactionsGiven ?? this.totalReactionsGiven,
      totalFollowers: totalFollowers ?? this.totalFollowers,
      totalFollowing: totalFollowing ?? this.totalFollowing,
      totalTimeSpent: totalTimeSpent ?? this.totalTimeSpent,
      firstLoginDate: firstLoginDate ?? this.firstLoginDate,
      lastActiveDate: lastActiveDate ?? this.lastActiveDate,
      totalSessions: totalSessions ?? this.totalSessions,
      averageSessionDuration: averageSessionDuration ?? this.averageSessionDuration,
      activeDaysCount: activeDaysCount ?? this.activeDaysCount,
      activityByDay: activityByDay ?? Map.from(this.activityByDay),
      activityByMonth: activityByMonth ?? Map.from(this.activityByMonth),
    );
  }

  @override
  String toString() {
    return 'UserStatistics(userId: $userId, userName: $userName, totalMindMaps: $totalMindMaps, totalPosts: $totalPosts)';
  }
}
