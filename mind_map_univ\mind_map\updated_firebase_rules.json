{"rules": {".read": "auth != null", ".write": "auth != null", "users": {".indexOn": ["email", "createdAt", "university", "major", "isAdmin"]}, "userProjects": {"$userId": {"mindMaps": {".indexOn": ["subject", "createdAt", "isPublished", "created<PERSON>y"]}, "subjects": {".indexOn": ["name", "createdAt"]}}}, "posts": {".indexOn": ["authorId", "createdAt", "isPublished"]}, "editRequests": {".indexOn": ["fromUserId", "toUserId", "status", "createdAt"]}, "notifications": {".indexOn": ["userId", "createdAt", "isRead"]}, "comments": {".indexOn": ["postId", "authorId", "createdAt"]}, "reactions": {".indexOn": ["postId", "userId", "createdAt"]}, "post_reports": {".indexOn": ["postId", "reporterId", "isResolved"]}, "user_warnings": {"$userId": {".indexOn": ["sentAt", "sentBy", "isRead"], ".read": "auth != null && (auth.uid == $userId || root.child('users').child(auth.uid).child('isAdmin').val() == true)", ".write": "auth != null && root.child('users').child(auth.uid).child('isAdmin').val() == true"}}, "user_actions": {"$userId": {".indexOn": ["actionType", "actionDate", "actionBy"], ".read": "auth != null && root.child('users').child(auth.uid).child('isAdmin').val() == true", ".write": "auth != null && root.child('users').child(auth.uid).child('isAdmin').val() == true"}}, "admin_statistics": {".read": "auth != null && root.child('users').child(auth.uid).child('isAdmin').val() == true", ".write": "auth != null && root.child('users').child(auth.uid).child('isAdmin').val() == true"}}}