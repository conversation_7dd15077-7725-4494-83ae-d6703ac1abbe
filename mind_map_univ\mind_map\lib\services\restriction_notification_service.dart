import 'package:firebase_database/firebase_database.dart';
import '../models/notification.dart';
import '../services/restriction_service.dart';

class RestrictionNotificationService {
  static final DatabaseReference _database = FirebaseDatabase.instance.ref();

  // إرسال إشعار تقييد للمستخدم
  static Future<void> sendRestrictionNotification({
    required String userId,
    required String reason,
    required int days,
    required List<String> restrictionTypes,
    required String restrictedBy,
  }) async {
    try {
      // تحويل أنواع التقييد إلى نص عربي
      final restrictionTypesText = restrictionTypes.map((type) {
        return RestrictionService.getRestrictionTypeName(type);
      }).join('، ');

      // إنشاء عنوان الإشعار
      String title = '⚠️ تم تقييد حسابك';
      
      // إنشاء محتوى الإشعار
      String message = _buildRestrictionMessage(
        reason: reason,
        days: days,
        restrictionTypes: restrictionTypesText,
        restrictedBy: restrictedBy,
      );

      // إنشاء الإشعار
      final notification = AppNotification(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        userId: userId,
        title: title,
        message: message,
        type: NotificationType.restriction,
        isRead: false,
        createdAt: DateTime.now(),
        additionalData: {
          'restrictionReason': reason,
          'restrictionDays': days,
          'restrictionTypes': restrictionTypes,
          'restrictedBy': restrictedBy,
          'restrictionEndDate': DateTime.now().add(Duration(days: days)).toIso8601String(),
        },
      );

      // حفظ الإشعار في Firebase
      await _database
          .child('notifications/${userId}/${notification.id}')
          .set(notification.toJson());

      print('✅ تم إرسال إشعار التقييد للمستخدم: $userId');
    } catch (e) {
      print('❌ خطأ في إرسال إشعار التقييد: $e');
    }
  }

  // إرسال إشعار إلغاء التقييد
  static Future<void> sendUnrestrictionNotification({
    required String userId,
    required String unrestrictedBy,
    bool isAutomatic = false,
  }) async {
    try {
      String title = '✅ تم إلغاء تقييد حسابك';
      
      String message = isAutomatic 
          ? _buildAutoUnrestrictionMessage()
          : _buildManualUnrestrictionMessage(unrestrictedBy);

      final notification = AppNotification(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        userId: userId,
        title: title,
        message: message,
        type: NotificationType.unrestriction,
        isRead: false,
        createdAt: DateTime.now(),
        additionalData: {
          'unrestrictedBy': unrestrictedBy,
          'isAutomatic': isAutomatic,
        },
      );

      await _database
          .child('notifications/${userId}/${notification.id}')
          .set(notification.toJson());

      print('✅ تم إرسال إشعار إلغاء التقييد للمستخدم: $userId');
    } catch (e) {
      print('❌ خطأ في إرسال إشعار إلغاء التقييد: $e');
    }
  }

  // إرسال إشعار تحذير
  static Future<void> sendWarningNotification({
    required String userId,
    required String reason,
    required String warnedBy,
  }) async {
    try {
      String title = '⚠️ تحذير من الإدارة';
      
      String message = _buildWarningMessage(reason, warnedBy);

      final notification = AppNotification(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        userId: userId,
        title: title,
        message: message,
        type: NotificationType.warning,
        isRead: false,
        createdAt: DateTime.now(),
        additionalData: {
          'warningReason': reason,
          'warnedBy': warnedBy,
        },
      );

      await _database
          .child('notifications/${userId}/${notification.id}')
          .set(notification.toJson());

      print('✅ تم إرسال إشعار التحذير للمستخدم: $userId');
    } catch (e) {
      print('❌ خطأ في إرسال إشعار التحذير: $e');
    }
  }

  // بناء رسالة التقييد
  static String _buildRestrictionMessage({
    required String reason,
    required int days,
    required String restrictionTypes,
    required String restrictedBy,
  }) {
    return 'تم تقييد حسابك لمدة $days ${days == 1 ? 'يوم' : 'أيام'}. اضغط لمعرفة التفاصيل.';
  }

  // بناء رسالة إلغاء التقييد اليدوي
  static String _buildManualUnrestrictionMessage(String unrestrictedBy) {
    return 'تم إلغاء تقييد حسابك من قبل الإدارة. مرحباً بعودتك! 🌟';
  }

  // بناء رسالة إلغاء التقييد التلقائي
  static String _buildAutoUnrestrictionMessage() {
    return 'انتهت مدة تقييد حسابك. يمكنك الآن استخدام التطبيق بحرية! 🌟';
  }

  // بناء رسالة التحذير
  static String _buildWarningMessage(String reason, String warnedBy) {
    return 'تلقيت تحذيراً من الإدارة. يرجى الالتزام بقوانين المجتمع.';
  }

  // إرسال إشعار عام من الإدارة
  static Future<void> sendAdminNotification({
    required String userId,
    required String title,
    required String message,
    required String adminName,
    Map<String, dynamic>? additionalData,
  }) async {
    try {
      final notification = AppNotification(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        userId: userId,
        title: '📢 $title',
        message: '$message\n\n👤 من: $adminName',
        type: NotificationType.adminMessage,
        isRead: false,
        createdAt: DateTime.now(),
        additionalData: {
          'adminName': adminName,
          ...?additionalData,
        },
      );

      await _database
          .child('notifications/${userId}/${notification.id}')
          .set(notification.toJson());

      print('✅ تم إرسال إشعار الإدارة للمستخدم: $userId');
    } catch (e) {
      print('❌ خطأ في إرسال إشعار الإدارة: $e');
    }
  }
}
