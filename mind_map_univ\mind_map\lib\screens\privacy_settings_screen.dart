import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/auth_provider.dart';
import '../services/restriction_service.dart';
import '../widgets/restriction_dialog.dart';
import '../widgets/connectivity_wrapper.dart';

class PrivacySettingsScreen extends StatefulWidget {
  const PrivacySettingsScreen({super.key});

  @override
  State<PrivacySettingsScreen> createState() => _PrivacySettingsScreenState();
}

class _PrivacySettingsScreenState extends State<PrivacySettingsScreen> {
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return ConnectivityWrapper(
      child: Scaffold(
      appBar: AppBar(
        title: const Text(
          'إعدادات الخصوصية',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.blue.shade700,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      backgroundColor: Colors.grey.shade50,
      body: Consumer<AuthProvider>(
        builder: (context, authProvider, child) {
          final userModel = authProvider.userModel;
          
          if (userModel == null) {
            return const Center(
              child: Text('لا توجد بيانات مستخدم متاحة'),
            );
          }

          return SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // رسالة تعليمية
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade50,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.blue.shade200),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.privacy_tip, color: Colors.blue.shade700),
                          const SizedBox(width: 8),
                          Text(
                            'حول إعدادات الخصوصية',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.blue.shade700,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      const Text(
                        'تحكم في من يمكنه رؤية ملفك الشخصي ومخططاتك الذهنية. '
                        'هذه الإعدادات تساعدك في حماية خصوصيتك والتحكم في مشاركة معلوماتك.',
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 24),

                // إعدادات الملف الشخصي
                _buildPrivacySection(
                  title: 'الملف الشخصي',
                  icon: Icons.person,
                  children: [
                    _buildPrivacySetting(
                      title: 'إظهار الملف الشخصي',
                      subtitle: 'السماح للآخرين برؤية ملفك الشخصي',
                      value: userModel.isProfilePublic ?? true,
                      onChanged: (value) => _updatePrivacySetting('isProfilePublic', value),
                    ),
                    _buildPrivacySetting(
                      title: 'إظهار البريد الإلكتروني',
                      subtitle: 'عرض بريدك الإلكتروني في الملف الشخصي',
                      value: userModel.showEmail ?? false,
                      onChanged: (value) => _updatePrivacySetting('showEmail', value),
                    ),
                    _buildPrivacySetting(
                      title: 'إظهار الجامعة',
                      subtitle: 'عرض جامعتك في الملف الشخصي',
                      value: userModel.showUniversity ?? true,
                      onChanged: (value) => _updatePrivacySetting('showUniversity', value),
                    ),
                    _buildPrivacySetting(
                      title: 'إظهار التخصص',
                      subtitle: 'عرض تخصصك في الملف الشخصي',
                      value: userModel.showMajor ?? true,
                      onChanged: (value) => _updatePrivacySetting('showMajor', value),
                    ),
                  ],
                ),

                const SizedBox(height: 20),

                // إعدادات المخططات الذهنية
                _buildPrivacySection(
                  title: 'المخططات الذهنية',
                  icon: Icons.account_tree,
                  children: [
                    _buildPrivacySetting(
                      title: 'المخططات العامة افتراضياً',
                      subtitle: 'جعل المخططات الجديدة مرئية للجميع',
                      value: userModel.defaultPublicMindMaps ?? false,
                      onChanged: (value) => _updatePrivacySetting('defaultPublicMindMaps', value),
                    ),
                    _buildPrivacySetting(
                      title: 'السماح بالتعليقات',
                      subtitle: 'السماح للآخرين بالتعليق على مخططاتك',
                      value: userModel.allowComments ?? true,
                      onChanged: (value) => _updatePrivacySetting('allowComments', value),
                    ),
                    _buildPrivacySetting(
                      title: 'السماح بالنسخ',
                      subtitle: 'السماح للآخرين بنسخ مخططاتك العامة',
                      value: userModel.allowCopying ?? true,
                      onChanged: (value) => _updatePrivacySetting('allowCopying', value),
                    ),
                  ],
                ),

                const SizedBox(height: 20),

                // إعدادات التفاعل الاجتماعي
                _buildPrivacySection(
                  title: 'التفاعل الاجتماعي',
                  icon: Icons.people,
                  children: [
                    _buildPrivacySetting(
                      title: 'السماح بالمتابعة',
                      subtitle: 'السماح للآخرين بمتابعتك',
                      value: userModel.allowFollowing ?? true,
                      onChanged: (value) => _updatePrivacySetting('allowFollowing', value),
                    ),
                    _buildPrivacySetting(
                      title: 'إظهار قائمة المتابعين',
                      subtitle: 'عرض قائمة متابعيك للآخرين',
                      value: userModel.showFollowers ?? true,
                      onChanged: (value) => _updatePrivacySetting('showFollowers', value),
                    ),
                    _buildPrivacySetting(
                      title: 'إظهار قائمة المتابَعين',
                      subtitle: 'عرض قائمة من تتابعهم للآخرين',
                      value: userModel.showFollowing ?? true,
                      onChanged: (value) => _updatePrivacySetting('showFollowing', value),
                    ),
                    _buildPrivacySetting(
                      title: 'إظهار متابعيني لمتابعيني فقط',
                      subtitle: 'قائمة متابعيك ستكون مرئية فقط لمن يتابعونك',
                      value: userModel.showFollowersToFollowersOnly ?? false,
                      onChanged: (value) => _updatePrivacySetting('showFollowersToFollowersOnly', value),
                    ),
                    _buildPrivacySetting(
                      title: 'إظهار متابَعيني لمتابعيني فقط',
                      subtitle: 'قائمة من تتابعهم ستكون مرئية فقط لمن يتابعونك',
                      value: userModel.showFollowingToFollowersOnly ?? false,
                      onChanged: (value) => _updatePrivacySetting('showFollowingToFollowersOnly', value),
                    ),
                  ],
                ),

                const SizedBox(height: 20),

                // إعدادات الإشعارات
                _buildPrivacySection(
                  title: 'الإشعارات',
                  icon: Icons.notifications,
                  children: [
                    _buildPrivacySetting(
                      title: 'إشعارات المتابعة',
                      subtitle: 'تلقي إشعار عند متابعة شخص جديد لك',
                      value: userModel.notifyOnFollow ?? true,
                      onChanged: (value) => _updatePrivacySetting('notifyOnFollow', value),
                    ),
                    _buildPrivacySetting(
                      title: 'إشعارات التعليقات',
                      subtitle: 'تلقي إشعار عند التعليق على مخططاتك',
                      value: userModel.notifyOnComment ?? true,
                      onChanged: (value) => _updatePrivacySetting('notifyOnComment', value),
                    ),
                    _buildPrivacySetting(
                      title: 'إشعارات ردود الفعل',
                      subtitle: 'تلقي إشعار عند إضافة رد فعل على منشوراتك',
                      value: userModel.notifyOnReaction ?? true,
                      onChanged: (value) => _updatePrivacySetting('notifyOnReaction', value),
                    ),
                    _buildPrivacySetting(
                      title: 'إشعار المتابعين عند النشر',
                      subtitle: 'إرسال إشعار لمتابعيك عند نشر منشور جديد',
                      value: userModel.notifyFollowersOnNewPost ?? true,
                      onChanged: (value) => _updatePrivacySetting('notifyFollowersOnNewPost', value),
                    ),
                  ],
                ),

                const SizedBox(height: 32),

                // زر حفظ الإعدادات
                SizedBox(
                  width: double.infinity,
                  height: 56,
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _saveAllSettings,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue.shade700,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      elevation: 2,
                    ),
                    child: _isLoading
                        ? const Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                ),
                              ),
                              SizedBox(width: 12),
                              Text(
                                'جاري الحفظ...',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          )
                        : const Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(Icons.save, size: 20),
                              SizedBox(width: 8),
                              Text(
                                'حفظ الإعدادات',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                  ),
                ),

                const SizedBox(height: 24),
              ],
            ),
          );
        },
      ),
    ),
    );
  }

  // بناء قسم إعدادات الخصوصية
  Widget _buildPrivacySection({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Icon(icon, color: Colors.blue.shade700, size: 24),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          const Divider(height: 1),
          ...children,
        ],
      ),
    );
  }

  // بناء إعداد خصوصية واحد
  Widget _buildPrivacySetting({
    required String title,
    required String subtitle,
    required bool value,
    required Function(bool) onChanged,
  }) {
    return ListTile(
      title: Text(
        title,
        style: const TextStyle(
          fontWeight: FontWeight.w600,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          color: Colors.grey.shade600,
          fontSize: 14,
        ),
      ),
      trailing: Switch(
        value: value,
        onChanged: (newValue) async {
          // التحقق من تقييد إعدادات الخصوصية
          final authProvider = Provider.of<AuthProvider>(context, listen: false);
          final currentUser = authProvider.userModel;

          if (currentUser != null) {
            final restriction = await RestrictionService.checkPrivacySettingsRestriction(currentUser.uid);
            if (restriction != null && mounted) {
              await RestrictionDialog.show(context, restriction, 'تعديل إعدادات الخصوصية');
              return;
            }
          }

          // تنفيذ التغيير إذا لم يكن هناك تقييد
          onChanged(newValue);
        },
        activeColor: Colors.blue.shade700,
      ),
    );
  }

  // تحديث إعداد خصوصية واحد
  void _updatePrivacySetting(String setting, bool value) async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final currentUser = authProvider.userModel;

    // التحقق من تقييد إعدادات الخصوصية
    if (currentUser != null) {
      final restriction = await RestrictionService.checkPrivacySettingsRestriction(currentUser.uid);
      if (restriction != null && mounted) {
        await RestrictionDialog.show(context, restriction, 'تعديل إعدادات الخصوصية');
        return;
      }
    }

    authProvider.updatePrivacySetting(setting, value);
  }

  // حفظ جميع الإعدادات
  Future<void> _saveAllSettings() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final success = await authProvider.savePrivacySettings();

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Row(
              children: [
                Icon(Icons.check_circle, color: Colors.white),
                SizedBox(width: 8),
                Text('تم حفظ إعدادات الخصوصية بنجاح'),
              ],
            ),
            backgroundColor: Colors.green.shade600,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Row(
              children: [
                Icon(Icons.error, color: Colors.white),
                SizedBox(width: 8),
                Text('فشل في حفظ إعدادات الخصوصية'),
              ],
            ),
            backgroundColor: Colors.red.shade600,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: Colors.white),
                const SizedBox(width: 8),
                Expanded(
                  child: Text('خطأ: ${e.toString()}'),
                ),
              ],
            ),
            backgroundColor: Colors.red.shade600,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
