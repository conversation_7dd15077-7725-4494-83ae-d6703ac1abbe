import 'package:flutter/material.dart';
import '../models/user_statistics.dart';

/// بطاقة ملخص إحصائيات المستخدم - يمكن استخدامها في أماكن مختلفة
class UserStatsSummaryCard extends StatelessWidget {
  final UserStatistics stats;
  final bool showDetailedStats;

  const UserStatsSummaryCard({
    super.key,
    required this.stats,
    this.showDetailedStats = false,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // العنوان
            Row(
              children: [
                Icon(
                  Icons.analytics,
                  color: Colors.blue.shade600,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'ملخص الإحصائيات',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey.shade800,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // الإحصائيات الأساسية
            Row(
              children: [
                Expanded(
                  child: _buildQuickStat(
                    'المشاريع',
                    stats.totalMindMaps.toString(),
                    Icons.psychology,
                    Colors.blue,
                  ),
                ),
                Expanded(
                  child: _buildQuickStat(
                    'المنشورات',
                    stats.totalPosts.toString(),
                    Icons.post_add,
                    Colors.green,
                  ),
                ),
              ],
            ),
            
            if (showDetailedStats) ...[
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    child: _buildQuickStat(
                      'الإعجابات',
                      stats.totalLikes.toString(),
                      Icons.thumb_up,
                      Colors.red,
                    ),
                  ),
                  Expanded(
                    child: _buildQuickStat(
                      'التعليقات',
                      stats.totalComments.toString(),
                      Icons.comment,
                      Colors.orange,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              
              // معلومات الوقت
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey.shade50,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  children: [
                    _buildTimeInfo(
                      'إجمالي الوقت',
                      _formatDuration(stats.totalTimeSpent),
                    ),
                    const SizedBox(height: 4),
                    _buildTimeInfo(
                      'متوسط يومي',
                      _formatDuration(stats.averageDailyTime),
                    ),
                    const SizedBox(height: 4),
                    _buildTimeInfo(
                      'معدل النشاط',
                      '${stats.dailyActivityRate.toStringAsFixed(1)}%',
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildQuickStat(String label, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(8),
      margin: const EdgeInsets.symmetric(horizontal: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: color,
            size: 20,
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            label,
            style: TextStyle(
              fontSize: 10,
              color: Colors.grey.shade600,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildTimeInfo(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade600,
          ),
        ),
        Text(
          value,
          style: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  String _formatDuration(Duration duration) {
    if (duration.inDays > 0) {
      return '${duration.inDays}ي ${duration.inHours % 24}س';
    } else if (duration.inHours > 0) {
      return '${duration.inHours}س ${duration.inMinutes % 60}د';
    } else {
      return '${duration.inMinutes}د';
    }
  }
}
