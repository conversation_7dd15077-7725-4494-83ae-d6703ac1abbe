import 'package:uuid/uuid.dart';

enum ReportReason {
  spam('محتوى مزعج'),
  inappropriate('محتوى غير مناسب'),
  harassment('تحرش أو إساءة'),
  falseInfo('معلومات خاطئة'),
  copyright('انتهاك حقوق الطبع'),
  violence('عنف أو تهديد'),
  other('أخرى');

  const ReportReason(this.displayName);
  final String displayName;
}

class PostReport {
  final String id;
  final String postId;
  final String reporterId;
  final String reporterName;
  final ReportReason reason;
  final String? description;
  final DateTime createdAt;
  final bool isResolved;
  final String? resolvedBy;
  final DateTime? resolvedAt;
  final String? adminNotes;

  PostReport({
    String? id,
    required this.postId,
    required this.reporterId,
    required this.reporterName,
    required this.reason,
    this.description,
    DateTime? createdAt,
    this.isResolved = false,
    this.resolvedBy,
    this.resolvedAt,
    this.adminNotes,
  }) : id = id ?? const Uuid().v4(),
       createdAt = createdAt ?? DateTime.now();

  // تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'postId': postId,
      'reporterId': reporterId,
      'reporterName': reporterName,
      'reason': reason.name,
      'description': description,
      'createdAt': createdAt.toIso8601String(),
      'isResolved': isResolved,
      'resolvedBy': resolvedBy,
      'resolvedAt': resolvedAt?.toIso8601String(),
      'adminNotes': adminNotes,
    };
  }

  // إنشاء من JSON
  factory PostReport.fromJson(Map<String, dynamic> json) {
    return PostReport(
      id: json['id'] ?? '',
      postId: json['postId'] ?? '',
      reporterId: json['reporterId'] ?? '',
      reporterName: json['reporterName'] ?? '',
      reason: ReportReason.values.firstWhere(
        (r) => r.name == json['reason'],
        orElse: () => ReportReason.other,
      ),
      description: json['description'],
      createdAt: json['createdAt'] != null
          ? DateTime.parse(json['createdAt'])
          : DateTime.now(),
      isResolved: json['isResolved'] ?? false,
      resolvedBy: json['resolvedBy'],
      resolvedAt: json['resolvedAt'] != null
          ? DateTime.parse(json['resolvedAt'])
          : null,
      adminNotes: json['adminNotes'],
    );
  }

  // إنشاء نسخة محدثة
  PostReport copyWith({
    bool? isResolved,
    String? resolvedBy,
    DateTime? resolvedAt,
    String? adminNotes,
  }) {
    return PostReport(
      id: id,
      postId: postId,
      reporterId: reporterId,
      reporterName: reporterName,
      reason: reason,
      description: description,
      createdAt: createdAt,
      isResolved: isResolved ?? this.isResolved,
      resolvedBy: resolvedBy ?? this.resolvedBy,
      resolvedAt: resolvedAt ?? this.resolvedAt,
      adminNotes: adminNotes ?? this.adminNotes,
    );
  }

  @override
  String toString() {
    return 'PostReport(id: $id, postId: $postId, reason: ${reason.displayName})';
  }
}
