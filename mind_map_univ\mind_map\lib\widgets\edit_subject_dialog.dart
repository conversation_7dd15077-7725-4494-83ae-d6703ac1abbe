import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/mind_map_provider.dart';
import '../models/subject.dart';

class EditSubjectDialog extends StatefulWidget {
  final Subject subject;

  const EditSubjectDialog({
    super.key,
    required this.subject,
  });

  @override
  State<EditSubjectDialog> createState() => _EditSubjectDialogState();
}

class _EditSubjectDialogState extends State<EditSubjectDialog> {
  final _formKey = GlobalKey<FormState>();
  late final TextEditingController _nameController;
  late final TextEditingController _descriptionController;
  late final TextEditingController _professorController;
  late Color _selectedColor;
  late int _semester;
  late double _creditHours;

  // Available colors for subjects
  final List<Color> _availableColors = [
    const Color(0xFF2196F3), // Blue
    const Color(0xFF4CAF50), // Green
    const Color(0xFFFF9800), // Orange
    const Color(0xFF9C27B0), // Purple
    const Color(0xFFF44336), // Red
    const Color(0xFF00BCD4), // Cyan
    const Color(0xFF795548), // Brown
    const Color(0xFF607D8B), // Blue Grey
    const Color(0xFFE91E63), // Pink
    const Color(0xFF3F51B5), // Indigo
  ];

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(text: widget.subject.name);
    _descriptionController = TextEditingController(text: widget.subject.description);
    _professorController = TextEditingController(text: widget.subject.professor ?? '');
    _selectedColor = widget.subject.color;
    _semester = widget.subject.semester;
    _creditHours = widget.subject.creditHours;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _professorController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('تحرير المادة الدراسية'),
      content: SizedBox(
        width: double.maxFinite,
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Name field
                TextFormField(
                  controller: _nameController,
                  decoration: const InputDecoration(
                    labelText: 'اسم المادة *',
                    hintText: 'أدخل اسم المادة الدراسية',
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'يرجى إدخال اسم المادة';
                    }
                    return null;
                  },
                ),

                const SizedBox(height: 16),

                // Description field
                TextFormField(
                  controller: _descriptionController,
                  decoration: const InputDecoration(
                    labelText: 'الوصف (اختياري)',
                    hintText: 'أدخل وصف المادة',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 2,
                ),

                const SizedBox(height: 16),

                // Professor field
                TextFormField(
                  controller: _professorController,
                  decoration: const InputDecoration(
                    labelText: 'اسم الأستاذ (اختياري)',
                    hintText: 'أدخل اسم أستاذ المادة',
                    border: OutlineInputBorder(),
                  ),
                ),

                const SizedBox(height: 16),

                // Semester and Credit Hours Row
                Row(
                  children: [
                    // Semester
                    Expanded(
                      child: DropdownButtonFormField<int>(
                        value: _semester,
                        decoration: const InputDecoration(
                          labelText: 'الفصل الدراسي',
                          border: OutlineInputBorder(),
                        ),
                        items: List.generate(8, (index) {
                          return DropdownMenuItem<int>(
                            value: index + 1,
                            child: Text('الفصل ${index + 1}'),
                          );
                        }),
                        onChanged: (value) {
                          setState(() {
                            _semester = value!;
                          });
                        },
                      ),
                    ),

                    const SizedBox(width: 16),

                    // Credit Hours
                    Expanded(
                      child: DropdownButtonFormField<double>(
                        value: _creditHours,
                        decoration: const InputDecoration(
                          labelText: 'الساعات المعتمدة',
                          border: OutlineInputBorder(),
                        ),
                        items: [
                          const DropdownMenuItem<double>(
                            value: 1.0,
                            child: Text('ساعة واحدة'),
                          ),
                          const DropdownMenuItem<double>(
                            value: 1.5,
                            child: Text('ساعة ونصف'),
                          ),
                          const DropdownMenuItem<double>(
                            value: 2.0,
                            child: Text('ساعتان'),
                          ),
                          const DropdownMenuItem<double>(
                            value: 2.5,
                            child: Text('ساعتان ونصف'),
                          ),
                          const DropdownMenuItem<double>(
                            value: 3.0,
                            child: Text('3 ساعات'),
                          ),
                          const DropdownMenuItem<double>(
                            value: 3.5,
                            child: Text('3 ساعات ونصف'),
                          ),
                          const DropdownMenuItem<double>(
                            value: 4.0,
                            child: Text('4 ساعات'),
                          ),
                          const DropdownMenuItem<double>(
                            value: 4.5,
                            child: Text('4 ساعات ونصف'),
                          ),
                          const DropdownMenuItem<double>(
                            value: 5.0,
                            child: Text('5 ساعات'),
                          ),
                          const DropdownMenuItem<double>(
                            value: 5.5,
                            child: Text('5 ساعات ونصف'),
                          ),
                          const DropdownMenuItem<double>(
                            value: 6.0,
                            child: Text('6 ساعات'),
                          ),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _creditHours = value!;
                          });
                        },
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                // Color selection
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'لون المادة',
                      style: TextStyle(fontWeight: FontWeight.w500),
                    ),
                    const SizedBox(height: 8),
                    Container(
                      height: 60,
                      child: GridView.builder(
                        scrollDirection: Axis.horizontal,
                        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 2,
                          crossAxisSpacing: 8,
                          mainAxisSpacing: 8,
                        ),
                        itemCount: _availableColors.length,
                        itemBuilder: (context, index) {
                          final color = _availableColors[index];
                          final isSelected = color == _selectedColor;
                          
                          return GestureDetector(
                            onTap: () {
                              setState(() {
                                _selectedColor = color;
                              });
                            },
                            child: Container(
                              decoration: BoxDecoration(
                                color: color,
                                shape: BoxShape.circle,
                                border: isSelected
                                    ? Border.all(color: Colors.white, width: 3)
                                    : null,
                                boxShadow: isSelected
                                    ? [
                                        BoxShadow(
                                          color: color.withValues(alpha: 0.5),
                                          blurRadius: 8,
                                          spreadRadius: 2,
                                        ),
                                      ]
                                    : null,
                              ),
                              child: isSelected
                                  ? const Icon(
                                      Icons.check,
                                      color: Colors.white,
                                      size: 16,
                                    )
                                  : null,
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                // Preview
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: _selectedColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: _selectedColor.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'معاينة المادة',
                        style: TextStyle(
                          fontWeight: FontWeight.w500,
                          color: _selectedColor,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        _nameController.text.isEmpty 
                            ? 'اسم المادة' 
                            : _nameController.text,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      if (_descriptionController.text.isNotEmpty) ...[
                        const SizedBox(height: 4),
                        Text(
                          _descriptionController.text,
                          style: TextStyle(
                            fontSize: 12,
                            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                          ),
                        ),
                      ],
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Text(
                            'الفصل $_semester',
                            style: const TextStyle(fontSize: 12),
                          ),
                          const SizedBox(width: 16),
                          Text(
                            _creditHours == _creditHours.toInt()
                                ? '${_creditHours.toInt()} ساعة'
                                : '$_creditHours ساعة',
                            style: const TextStyle(fontSize: 12),
                          ),
                          if (_professorController.text.isNotEmpty) ...[
                            const SizedBox(width: 16),
                            Text(
                              'د. ${_professorController.text}',
                              style: const TextStyle(fontSize: 12),
                            ),
                          ],
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: _updateSubject,
          child: const Text('حفظ التغييرات'),
        ),
      ],
    );
  }

  void _updateSubject() {
    if (_formKey.currentState!.validate()) {
      final provider = context.read<MindMapProvider>();
      
      // تحديث معلومات المادة
      widget.subject.updateInfo(
        newName: _nameController.text.trim(),
        newDescription: _descriptionController.text.trim(),
        newProfessor: _professorController.text.trim().isEmpty
            ? null
            : _professorController.text.trim(),
        newSemester: _semester,
        newCreditHours: _creditHours,
      );

      // تحديث اللون
      widget.subject.updateColor(_selectedColor);
      
      // حفظ التغييرات
      provider.updateSubject(widget.subject);
      
      Navigator.pop(context);
      
      // إظهار رسالة نجاح
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('تم تحديث معلومات "${widget.subject.name}" بنجاح'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }
}
