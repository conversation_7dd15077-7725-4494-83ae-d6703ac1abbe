import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:firebase_database/firebase_database.dart';
import '../models/edit_request.dart';
import '../models/mind_map.dart';
import '../providers/edit_requests_provider.dart';
import '../providers/mind_map_provider.dart';
import '../screens/mind_map_editor_screen.dart';

class EditRequestCard extends StatelessWidget {
  final EditRequest request;
  final bool isReceived;
  final VoidCallback? onAccept;
  final VoidCallback? onReject;

  const EditRequestCard({
    super.key,
    required this.request,
    required this.isReceived,
    this.onAccept,
    this.onReject,
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 400;

    return Card(
      margin: EdgeInsets.only(
        bottom: isSmallScreen ? 8 : 12,
        left: isSmallScreen ? 8 : 0,
        right: isSmallScreen ? 8 : 0,
      ),
      elevation: isSmallScreen ? 2 : 3,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(isSmallScreen ? 8 : 12),
        side: BorderSide(
          color: _getStatusColor(),
          width: isSmallScreen ? 1.5 : 2,
        ),
      ),
      child: Padding(
        padding: EdgeInsets.all(isSmallScreen ? 12 : 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // رأس البطاقة
            _buildHeader(),
            SizedBox(height: isSmallScreen ? 8 : 12),

            // معلومات المخطط
            _buildMindMapInfo(),
            SizedBox(height: isSmallScreen ? 8 : 12),

            // رسالة الطلب
            _buildRequestMessage(),

            // رسالة الرد (إن وجدت)
            if (request.responseMessage != null) ...[
              SizedBox(height: isSmallScreen ? 8 : 12),
              _buildResponseMessage(),
            ],

            SizedBox(height: isSmallScreen ? 12 : 16),

            // أزرار الإجراءات
            _buildActionButtons(context),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return LayoutBuilder(
      builder: (context, constraints) {
        final isSmallScreen = constraints.maxWidth < 350;

        if (isSmallScreen) {
          // تخطيط عمودي للشاشات الصغيرة
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  // أيقونة المستخدم
                  CircleAvatar(
                    radius: 18,
                    backgroundColor: Colors.blue.shade100,
                    child: Text(
                      _getAuthorInitial(),
                      style: TextStyle(
                        color: Colors.blue.shade700,
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                    ),
                  ),
                  const SizedBox(width: 10),

                  // معلومات المستخدم
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          isReceived ? request.fromUserName : request.toUserName,
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 14,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                        Text(
                          isReceived ? request.fromUserUniversity : 'إلى: ${request.toUserName}',
                          style: TextStyle(
                            color: Colors.grey.shade600,
                            fontSize: 12,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              // حالة الطلب
              Align(
                alignment: Alignment.centerLeft,
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: _getStatusColor(),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    request.statusText,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 11,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ],
          );
        } else {
          // تخطيط أفقي للشاشات الكبيرة
          return Row(
            children: [
              // أيقونة المستخدم
              CircleAvatar(
                radius: 20,
                backgroundColor: Colors.blue.shade100,
                child: Text(
                  _getAuthorInitial(),
                  style: TextStyle(
                    color: Colors.blue.shade700,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              const SizedBox(width: 12),

              // معلومات المستخدم
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      isReceived ? request.fromUserName : request.toUserName,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                    Text(
                      isReceived ? request.fromUserUniversity : 'إلى: ${request.toUserName}',
                      style: TextStyle(
                        color: Colors.grey.shade600,
                        fontSize: 14,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),

              // حالة الطلب
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: _getStatusColor(),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  request.statusText,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          );
        }
      },
    );
  }

  Widget _buildMindMapInfo() {
    return LayoutBuilder(
      builder: (context, constraints) {
        final isSmallScreen = constraints.maxWidth < 350;

        return Container(
          padding: EdgeInsets.all(isSmallScreen ? 10 : 12),
          decoration: BoxDecoration(
            color: Colors.grey.shade50,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey.shade200),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.account_tree,
                    color: Colors.blue.shade600,
                    size: isSmallScreen ? 18 : 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      request.mindMapTitle,
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: isSmallScreen ? 14 : 16,
                      ),
                      overflow: TextOverflow.ellipsis,
                      maxLines: isSmallScreen ? 2 : 1,
                    ),
                  ),
                ],
              ),
              if (request.mindMapSubject.isNotEmpty) ...[
                const SizedBox(height: 4),
                Text(
                  'المادة: ${request.mindMapSubject}',
                  style: TextStyle(
                    color: Colors.grey.shade600,
                    fontSize: isSmallScreen ? 12 : 14,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ],
          ),
        );
      },
    );
  }

  Widget _buildRequestMessage() {
    return LayoutBuilder(
      builder: (context, constraints) {
        final isSmallScreen = constraints.maxWidth < 350;

        return Container(
          width: double.infinity,
          padding: EdgeInsets.all(isSmallScreen ? 10 : 12),
          decoration: BoxDecoration(
            color: Colors.blue.shade50,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.blue.shade200),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'رسالة الطلب:',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.blue.shade700,
                  fontSize: isSmallScreen ? 12 : 14,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                request.requestMessage,
                style: TextStyle(
                  fontSize: isSmallScreen ? 12 : 14,
                ),
                maxLines: isSmallScreen ? 3 : null,
                overflow: isSmallScreen ? TextOverflow.ellipsis : null,
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildResponseMessage() {
    return LayoutBuilder(
      builder: (context, constraints) {
        final isSmallScreen = constraints.maxWidth < 350;

        return Container(
          width: double.infinity,
          padding: EdgeInsets.all(isSmallScreen ? 10 : 12),
          decoration: BoxDecoration(
            color: request.status == EditRequestStatus.accepted
                ? Colors.green.shade50
                : Colors.red.shade50,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: request.status == EditRequestStatus.accepted
                  ? Colors.green.shade200
                  : Colors.red.shade200,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'الرد:',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: request.status == EditRequestStatus.accepted
                      ? Colors.green.shade700
                      : Colors.red.shade700,
                  fontSize: isSmallScreen ? 12 : 14,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                request.responseMessage!,
                style: TextStyle(
                  fontSize: isSmallScreen ? 12 : 14,
                ),
                maxLines: isSmallScreen ? 3 : null,
                overflow: isSmallScreen ? TextOverflow.ellipsis : null,
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 400;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // تاريخ الطلب
        Text(
          _formatDate(request.createdAt),
          style: TextStyle(
            color: Colors.grey.shade500,
            fontSize: 12,
          ),
        ),
        const SizedBox(height: 12),

        // أزرار الإجراءات للطلبات الواردة المعلقة
        if (isReceived && request.status == EditRequestStatus.pending)
          _buildPendingButtons(context, isSmallScreen),

        // زر بدء التعديل للطلبات المقبولة (للمستخدم الذي طلب التعديل)
        if (!isReceived && request.status == EditRequestStatus.accepted)
          _buildStartEditingButton(context),

        // أزرار إعادة النشر للطلبات في انتظار الموافقة (لصاحب المنشور)
        if (isReceived && request.status == EditRequestStatus.awaitingApproval)
          _buildApprovalButtons(context, isSmallScreen),
      ],
    );
  }

  Widget _buildPendingButtons(BuildContext context, bool isSmallScreen) {
    if (isSmallScreen) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          ElevatedButton(
            onPressed: onAccept,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
            child: const Text('قبول'),
          ),
          const SizedBox(height: 8),
          TextButton(
            onPressed: onReject,
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: const Text('رفض'),
          ),
        ],
      );
    } else {
      return Row(
        children: [
          Expanded(
            child: TextButton(
              onPressed: onReject,
              style: TextButton.styleFrom(
                foregroundColor: Colors.red,
              ),
              child: const Text('رفض'),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: ElevatedButton(
              onPressed: onAccept,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
              ),
              child: const Text('قبول'),
            ),
          ),
        ],
      );
    }
  }

  Widget _buildStartEditingButton(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: () => _startEditing(context),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.blue,
          foregroundColor: Colors.white,
        ),
        icon: const Icon(Icons.edit),
        label: const Text('بدء التعديل'),
      ),
    );
  }

  Widget _buildApprovalButtons(BuildContext context, bool isSmallScreen) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // زر المعاينة
        OutlinedButton.icon(
          onPressed: () => _previewEdit(context),
          style: OutlinedButton.styleFrom(
            foregroundColor: Colors.blue,
            side: const BorderSide(color: Colors.blue),
          ),
          icon: const Icon(Icons.preview),
          label: const Text('معاينة التعديلات'),
        ),
        const SizedBox(height: 8),
        // أزرار الموافقة والرفض
        if (isSmallScreen) ...[
          ElevatedButton.icon(
            onPressed: () => _republishWithEdit(context),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
            icon: const Icon(Icons.publish),
            label: const Text('إعادة النشر'),
          ),
          const SizedBox(height: 8),
          TextButton(
            onPressed: () => _rejectFinalEdit(context),
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: const Text('رفض التعديل'),
          ),
        ] else ...[
          Row(
            children: [
              Expanded(
                child: TextButton(
                  onPressed: () => _rejectFinalEdit(context),
                  style: TextButton.styleFrom(
                    foregroundColor: Colors.red,
                  ),
                  child: const Text('رفض التعديل'),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () => _republishWithEdit(context),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                  ),
                  icon: const Icon(Icons.publish),
                  label: const Text('إعادة النشر'),
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  Color _getStatusColor() {
    switch (request.status) {
      case EditRequestStatus.pending:
        return Colors.orange;
      case EditRequestStatus.accepted:
        return Colors.green;
      case EditRequestStatus.rejected:
        return Colors.red;
      case EditRequestStatus.inProgress:
        return Colors.purple;
      case EditRequestStatus.awaitingApproval:
        return Colors.teal;
      case EditRequestStatus.completed:
        return Colors.blue;
    }
  }

  String _getAuthorInitial() {
    final name = isReceived ? request.fromUserName : request.toUserName;
    if (name.trim().isEmpty) {
      return 'م';
    }
    return name.trim()[0].toUpperCase();
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} يوم';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }

  void _startEditing(BuildContext context) async {
    try {
      // الحصول على المزودات قبل العمليات غير المتزامنة
      final editRequestsProvider = context.read<EditRequestsProvider>();
      final mindMapProvider = context.read<MindMapProvider>();

      // تحديث حالة الطلب إلى "قيد التعديل"
      await editRequestsProvider.startEditing(request.id);

      // محاولة البحث عن المخطط الذهني محلياً أولاً
      MindMap? mindMap;
      try {
        mindMap = mindMapProvider.mindMaps.firstWhere(
          (map) => map.id == request.mindMapId,
        );
      } catch (e) {
        // إذا لم يوجد محلياً، تحميله من Firebase
        print('🔄 تحميل المخطط الذهني من Firebase: ${request.mindMapId}');
        await mindMapProvider.loadMindMapById(request.mindMapId);

        // محاولة البحث مرة أخرى
        try {
          mindMap = mindMapProvider.mindMaps.firstWhere(
            (map) => map.id == request.mindMapId,
          );
        } catch (e) {
          throw Exception('لا يمكن العثور على المخطط الذهني');
        }
      }

      if (context.mounted) {
        // إنشاء نسخة للتعديل مع معرف مؤقت
        final editableMindMap = MindMap(
          id: '${mindMap.id}_edit_${request.id}', // معرف مؤقت للتعديل
          title: mindMap.title,
          description: mindMap.description,
          subject: mindMap.subject,
          nodes: Map.from(mindMap.nodes),
          connections: Map.from(mindMap.connections), // نسخ الروابط أيضاً
          comments: Map.from(mindMap.comments), // نسخ التعليقات أيضاً
          rootNodeId: mindMap.rootNodeId,
          tags: List.from(mindMap.tags),
          createdAt: mindMap.createdAt,
          updatedAt: DateTime.now(),
          isPublished: false,
          isFavorite: false,
        );

        // التنقل إلى محرر المخطط الذهني
        final result = await Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => MindMapEditorScreen(
              mindMap: editableMindMap,
              editRequestId: request.id, // تمرير معرف طلب التعديل
            ),
          ),
        );

        // إذا تم حفظ التعديل، إرساله للموافقة
        if (result == true && context.mounted) {
          // البحث عن المخطط المعدل
          final editedMindMapId = '${mindMap.id}_edit_${request.id}';
          final editedMindMap = mindMapProvider.mindMaps.firstWhere(
            (map) => map.id == editedMindMapId,
            orElse: () => throw Exception('المخطط المعدل غير موجود'),
          );

          await editRequestsProvider.submitForApproval(
            request.id,
            editedMindMapData: editedMindMap.toJson(),
          );

          if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تم إرسال التعديل لصاحب المنشور للموافقة'),
                backgroundColor: Colors.green,
              ),
            );
          }
        }
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // رفض التعديل النهائي
  void _rejectFinalEdit(BuildContext context) async {
    try {
      await context.read<EditRequestsProvider>().rejectRequest(request.id, responseMessage: 'تم رفض التعديل النهائي');

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم رفض التعديل'),
            backgroundColor: Colors.orange,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // معاينة التعديلات
  void _previewEdit(BuildContext context) async {
    try {
      // الحصول على بيانات المخطط المعدل من Firebase
      final requestRef = FirebaseDatabase.instance.ref('editRequests/${request.id}');
      final requestSnapshot = await requestRef.once();

      if (!requestSnapshot.snapshot.exists) {
        throw Exception('طلب التعديل غير موجود');
      }

      final requestData = requestSnapshot.snapshot.value as Map<dynamic, dynamic>;
      final editedMindMapData = requestData['editedMindMapData'];

      if (editedMindMapData == null) {
        throw Exception('بيانات المخطط المعدل غير موجودة');
      }

      // تحويل البيانات بشكل آمن
      Map<String, dynamic> convertedData = {};

      if (editedMindMapData is Map) {
        editedMindMapData.forEach((key, value) {
          if (key != null) {
            convertedData[key.toString()] = _convertValue(value);
          }
        });
      }

      // تحويل البيانات إلى MindMap
      final editedMindMap = MindMap.fromJson(convertedData);

      if (context.mounted) {
        // التنقل إلى شاشة المعاينة
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => Scaffold(
              appBar: AppBar(
                title: Text('معاينة التعديلات - ${editedMindMap.title}'),
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
              ),
              body: MindMapEditorScreen(
                mindMap: editedMindMap,
                isReadOnly: true, // وضع القراءة فقط للمعاينة
              ),
            ),
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل المعاينة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // دالة مساعدة لتحويل القيم بشكل آمن
  dynamic _convertValue(dynamic value) {
    if (value == null) return null;

    if (value is Map) {
      Map<String, dynamic> convertedMap = {};
      value.forEach((key, val) {
        if (key != null) {
          convertedMap[key.toString()] = _convertValue(val);
        }
      });
      return convertedMap;
    } else if (value is List) {
      return value.map((item) => _convertValue(item)).toList();
    } else {
      return value;
    }
  }

  // إعادة النشر مع التعديل
  void _republishWithEdit(BuildContext context) async {
    try {
      // الحصول على المزود
      final editRequestsProvider = context.read<EditRequestsProvider>();

      // إعادة النشر مع التعديل (البيانات محفوظة في طلب التعديل)
      await editRequestsProvider.republishWithEdit(request.id);

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إعادة نشر المنشور مع التعديل'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
