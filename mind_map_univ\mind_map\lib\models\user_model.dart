class UserModel {
  final String uid;
  final String email;
  final String firstName;
  final String lastName;
  final String university;
  final String major;
  final String bio; // النبذة الشخصية
  final DateTime birthDate;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime lastActiveAt; // آخر نشاط للمستخدم
  final List<String> following; // قائمة المستخدمين الذين يتابعهم
  final List<String> followers; // قائمة المتابعين
  final bool isAdmin; // هل المستخدم مدير

  // إعدادات التقييد والحظر
  final bool? isRestricted; // هل المستخدم مقيد
  final String? restrictionReason; // سبب التقييد
  final DateTime? restrictedAt; // تاريخ التقييد
  final DateTime? restrictionEndDate; // تاريخ انتهاء التقييد
  final int? restrictionDays; // عدد أيام التقييد
  final List<String>? restrictionTypes; // أنواع التقييد
  final String? restrictedBy; // من قام بالتقييد

  // إعدادات الحظر
  final bool? isBanned; // هل المستخدم محظور
  final String? banReason; // سبب الحظر
  final DateTime? bannedAt; // تاريخ الحظر
  final String? bannedBy; // من قام بالحظر

  // إعدادات الخصوصية
  final bool? isProfilePublic; // إظهار الملف الشخصي
  final bool? showEmail; // إظهار البريد الإلكتروني
  final bool? showUniversity; // إظهار الجامعة
  final bool? showMajor; // إظهار التخصص
  final bool? defaultPublicMindMaps; // المخططات العامة افتراضياً
  final bool? allowComments; // السماح بالتعليقات
  final bool? allowCopying; // السماح بالنسخ
  final bool? allowFollowing; // السماح بالمتابعة
  final bool? showFollowers; // إظهار قائمة المتابعين
  final bool? showFollowing; // إظهار قائمة المتابَعين
  final bool? showFollowersToFollowersOnly; // إظهار المتابعين للذين يتابعوني فقط
  final bool? showFollowingToFollowersOnly; // إظهار المتابَعين لمتابعيني فقط
  final bool? notifyOnFollow; // إشعارات المتابعة
  final bool? notifyOnComment; // إشعارات التعليقات
  final bool? notifyOnReaction; // إشعارات ردود الفعل
  final bool? notifyFollowersOnNewPost; // إشعار المتابعين عند نشر منشور جديد

  UserModel({
    required this.uid,
    required this.email,
    required this.firstName,
    required this.lastName,
    required this.university,
    required this.major,
    this.bio = '', // النبذة الشخصية اختيارية
    required this.birthDate,
    required this.createdAt,
    required this.updatedAt,
    DateTime? lastActiveAt,
    List<String>? following,
    List<String>? followers,
    this.isAdmin = false, // افتراضياً ليس مدير
    // إعدادات التقييد والحظر
    this.isRestricted,
    this.restrictionReason,
    this.restrictedAt,
    this.restrictionEndDate,
    this.restrictionDays,
    this.restrictionTypes,
    this.restrictedBy,
    // إعدادات الحظر
    this.isBanned,
    this.banReason,
    this.bannedAt,
    this.bannedBy,
    // إعدادات الخصوصية
    this.isProfilePublic,
    this.showEmail,
    this.showUniversity,
    this.showMajor,
    this.defaultPublicMindMaps,
    this.allowComments,
    this.allowCopying,
    this.allowFollowing,
    this.showFollowers,
    this.showFollowing,
    this.showFollowersToFollowersOnly,
    this.showFollowingToFollowersOnly,
    this.notifyOnFollow,
    this.notifyOnComment,
    this.notifyOnReaction,
    this.notifyFollowersOnNewPost,
  }) : lastActiveAt = lastActiveAt ?? DateTime.now(),
       following = following ?? [],
       followers = followers ?? [];

  // حساب العمر تلقائياً
  int get age {
    final now = DateTime.now();
    int age = now.year - birthDate.year;
    
    // التحقق من عدم حلول عيد الميلاد بعد
    if (now.month < birthDate.month || 
        (now.month == birthDate.month && now.day < birthDate.day)) {
      age--;
    }
    
    return age;
  }

  // الاسم الكامل
  String get fullName => '$firstName $lastName';

  // حالة النشاط - يعتبر المستخدم نشط إذا كان آخر نشاط له خلال آخر 5 دقائق
  bool get isActive {
    final now = DateTime.now();
    final difference = now.difference(lastActiveAt);
    return difference.inMinutes <= 5;
  }

  // وقت عدم النشاط
  String get inactiveTime {
    if (isActive) return '';

    final now = DateTime.now();
    final difference = now.difference(lastActiveAt);

    if (difference.inMinutes < 60) {
      return '${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return '${difference.inHours} ساعة';
    } else if (difference.inDays < 30) {
      return '${difference.inDays} يوم';
    } else if (difference.inDays < 365) {
      final months = (difference.inDays / 30).floor();
      return '$months شهر';
    } else {
      final years = (difference.inDays / 365).floor();
      return '$years سنة';
    }
  }

  // عدد المتابعين
  int get followersCount => followers.length;

  // عدد المتابَعين
  int get followingCount => following.length;

  // التحقق من متابعة مستخدم معين
  bool isFollowing(String userId) => following.contains(userId);

  // التحقق من كون مستخدم معين متابع
  bool isFollowedBy(String userId) => followers.contains(userId);

  // التحقق من إمكانية رؤية قائمة المتابعين
  bool canViewFollowers(String? viewerUserId) {
    print('🔍 canViewFollowers Debug:');
    print('   المستخدم المعروض: $fullName ($uid)');
    print('   المشاهد: $viewerUserId');
    print('   showFollowers: ${showFollowers ?? true}');
    print('   showFollowersToFollowersOnly: ${showFollowersToFollowersOnly ?? false}');
    print('   قائمة المتابعين: $followers');

    // إذا كان المشاهد هو نفس المستخدم - يرى متابعيه دائماً
    if (viewerUserId == uid) {
      print('   ✅ المشاهد هو نفس المستخدم - يرى متابعيه دائماً');
      return true;
    }

    // إذا كان الإعداد مفعل لإظهار المتابعين للمتابعين فقط
    if (showFollowersToFollowersOnly ?? false) {
      final isFollower = viewerUserId != null && followers.contains(viewerUserId);
      print('   🔒 الإعداد مفعل - هل المشاهد متابع؟ $isFollower');
      return isFollower;
    }

    // إذا كانت قائمة المتابعين مخفية تماماً (بعد فحص الإعداد الخاص)
    if (!(showFollowers ?? true)) {
      print('   ❌ قائمة المتابعين مخفية تماماً');
      return false;
    }

    // الإعداد العادي - إظهار للجميع
    print('   ✅ الإعداد العادي - إظهار للجميع');
    return true;
  }

  // التحقق من إمكانية رؤية قائمة المتابَعين
  bool canViewFollowing(String? viewerUserId) {
    print('🔍 canViewFollowing Debug:');
    print('   المستخدم المعروض: $fullName ($uid)');
    print('   المشاهد: $viewerUserId');
    print('   showFollowing: ${showFollowing ?? true}');
    print('   showFollowingToFollowersOnly: ${showFollowingToFollowersOnly ?? false}');
    print('   قائمة المتابعين: $followers');

    // إذا كان المشاهد هو نفس المستخدم - يرى متابَعيه دائماً
    if (viewerUserId == uid) {
      print('   ✅ المشاهد هو نفس المستخدم - يرى متابَعيه دائماً');
      return true;
    }

    // إذا كان الإعداد مفعل لإظهار المتابَعين للمتابعين فقط
    if (showFollowingToFollowersOnly ?? false) {
      final isFollower = viewerUserId != null && followers.contains(viewerUserId);
      print('   🔒 الإعداد مفعل - هل المشاهد متابع؟ $isFollower');
      return isFollower;
    }

    // إذا كانت قائمة المتابَعين مخفية تماماً (بعد فحص الإعداد الخاص)
    if (!(showFollowing ?? true)) {
      print('   ❌ قائمة المتابَعين مخفية تماماً');
      return false;
    }

    // الإعداد العادي - إظهار للجميع
    print('   ✅ الإعداد العادي - إظهار للجميع');
    return true;
  }

  // تحويل إلى Map للحفظ في Firebase
  Map<String, dynamic> toMap() {
    return {
      'uid': uid,
      'email': email,
      'firstName': firstName,
      'lastName': lastName,
      'university': university,
      'major': major,
      'bio': bio,
      'birthDate': birthDate.toIso8601String(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'lastActiveAt': lastActiveAt.toIso8601String(),
      'following': following,
      'followers': followers,
      'isAdmin': isAdmin,
      // إعدادات التقييد والحظر
      'isRestricted': isRestricted,
      'restrictionReason': restrictionReason,
      'restrictedAt': restrictedAt?.toIso8601String(),
      'restrictionEndDate': restrictionEndDate?.toIso8601String(),
      'restrictionDays': restrictionDays,
      'restrictionTypes': restrictionTypes,
      'restrictedBy': restrictedBy,
      // إعدادات الحظر
      'isBanned': isBanned,
      'banReason': banReason,
      'bannedAt': bannedAt?.toIso8601String(),
      'bannedBy': bannedBy,
      // إعدادات الخصوصية
      'isProfilePublic': isProfilePublic,
      'showEmail': showEmail,
      'showUniversity': showUniversity,
      'showMajor': showMajor,
      'defaultPublicMindMaps': defaultPublicMindMaps,
      'allowComments': allowComments,
      'allowCopying': allowCopying,
      'allowFollowing': allowFollowing,
      'showFollowers': showFollowers,
      'showFollowing': showFollowing,
      'showFollowersToFollowersOnly': showFollowersToFollowersOnly,
      'showFollowingToFollowersOnly': showFollowingToFollowersOnly,
      'notifyOnFollow': notifyOnFollow,
      'notifyOnComment': notifyOnComment,
      'notifyOnReaction': notifyOnReaction,
      'notifyFollowersOnNewPost': notifyFollowersOnNewPost,
    };
  }

  // إنشاء من Map (من Firebase)
  factory UserModel.fromMap(Map<String, dynamic> map) {
    return UserModel(
      uid: map['uid'] ?? '',
      email: map['email'] ?? '',
      firstName: map['firstName'] ?? '',
      lastName: map['lastName'] ?? '',
      university: map['university'] ?? '',
      major: map['major'] ?? '',
      bio: map['bio'] ?? '',
      birthDate: DateTime.parse(map['birthDate']),
      createdAt: DateTime.parse(map['createdAt']),
      updatedAt: DateTime.parse(map['updatedAt']),
      lastActiveAt: map['lastActiveAt'] != null
          ? DateTime.parse(map['lastActiveAt'])
          : DateTime.now(),
      following: List<String>.from(map['following'] ?? []),
      followers: List<String>.from(map['followers'] ?? []),
      isAdmin: map['isAdmin'] ?? false,
      // إعدادات التقييد والحظر
      isRestricted: map['isRestricted'],
      restrictionReason: map['restrictionReason'],
      restrictedAt: map['restrictedAt'] != null
          ? DateTime.parse(map['restrictedAt'])
          : null,
      restrictionEndDate: map['restrictionEndDate'] != null
          ? DateTime.parse(map['restrictionEndDate'])
          : null,
      restrictionDays: map['restrictionDays'],
      restrictionTypes: map['restrictionTypes'] != null
          ? List<String>.from(map['restrictionTypes'])
          : null,
      restrictedBy: map['restrictedBy'],
      // إعدادات الحظر
      isBanned: map['isBanned'],
      banReason: map['banReason'],
      bannedAt: map['bannedAt'] != null
          ? DateTime.parse(map['bannedAt'])
          : null,
      bannedBy: map['bannedBy'],
      // إعدادات الخصوصية
      isProfilePublic: map['isProfilePublic'],
      showEmail: map['showEmail'],
      showUniversity: map['showUniversity'],
      showMajor: map['showMajor'],
      defaultPublicMindMaps: map['defaultPublicMindMaps'],
      allowComments: map['allowComments'],
      allowCopying: map['allowCopying'],
      allowFollowing: map['allowFollowing'],
      showFollowers: map['showFollowers'],
      showFollowing: map['showFollowing'],
      showFollowersToFollowersOnly: map['showFollowersToFollowersOnly'],
      showFollowingToFollowersOnly: map['showFollowingToFollowersOnly'],
      notifyOnFollow: map['notifyOnFollow'],
      notifyOnComment: map['notifyOnComment'],
      notifyOnReaction: map['notifyOnReaction'],
      notifyFollowersOnNewPost: map['notifyFollowersOnNewPost'],
    );
  }

  // نسخ مع تعديل بعض الحقول
  UserModel copyWith({
    String? uid,
    String? email,
    String? firstName,
    String? lastName,
    String? university,
    String? major,
    String? bio,
    DateTime? birthDate,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? lastActiveAt,
    List<String>? following,
    List<String>? followers,
    bool? isAdmin,
    // إعدادات الخصوصية
    bool? isProfilePublic,
    bool? showEmail,
    bool? showUniversity,
    bool? showMajor,
    bool? defaultPublicMindMaps,
    bool? allowComments,
    bool? allowCopying,
    bool? allowFollowing,
    bool? showFollowers,
    bool? showFollowing,
    bool? showFollowersToFollowersOnly,
    bool? showFollowingToFollowersOnly,
    bool? notifyOnFollow,
    bool? notifyOnComment,
    bool? notifyOnReaction,
    bool? notifyFollowersOnNewPost,
  }) {
    return UserModel(
      uid: uid ?? this.uid,
      email: email ?? this.email,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      university: university ?? this.university,
      major: major ?? this.major,
      bio: bio ?? this.bio,
      birthDate: birthDate ?? this.birthDate,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      lastActiveAt: lastActiveAt ?? this.lastActiveAt,
      following: following ?? List.from(this.following),
      followers: followers ?? List.from(this.followers),
      isAdmin: isAdmin ?? this.isAdmin,
      // إعدادات الخصوصية
      isProfilePublic: isProfilePublic ?? this.isProfilePublic,
      showEmail: showEmail ?? this.showEmail,
      showUniversity: showUniversity ?? this.showUniversity,
      showMajor: showMajor ?? this.showMajor,
      defaultPublicMindMaps: defaultPublicMindMaps ?? this.defaultPublicMindMaps,
      allowComments: allowComments ?? this.allowComments,
      allowCopying: allowCopying ?? this.allowCopying,
      allowFollowing: allowFollowing ?? this.allowFollowing,
      showFollowers: showFollowers ?? this.showFollowers,
      showFollowing: showFollowing ?? this.showFollowing,
      showFollowersToFollowersOnly: showFollowersToFollowersOnly ?? this.showFollowersToFollowersOnly,
      showFollowingToFollowersOnly: showFollowingToFollowersOnly ?? this.showFollowingToFollowersOnly,
      notifyOnFollow: notifyOnFollow ?? this.notifyOnFollow,
      notifyOnComment: notifyOnComment ?? this.notifyOnComment,
      notifyOnReaction: notifyOnReaction ?? this.notifyOnReaction,
      notifyFollowersOnNewPost: notifyFollowersOnNewPost ?? this.notifyFollowersOnNewPost,
    );
  }

  @override
  String toString() {
    return 'UserModel(uid: $uid, email: $email, fullName: $fullName, university: $university, major: $major, age: $age)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is UserModel &&
      other.uid == uid &&
      other.email == email &&
      other.firstName == firstName &&
      other.lastName == lastName &&
      other.university == university &&
      other.major == major &&
      other.birthDate == birthDate;
  }

  @override
  int get hashCode {
    return uid.hashCode ^
      email.hashCode ^
      firstName.hashCode ^
      lastName.hashCode ^
      university.hashCode ^
      major.hashCode ^
      birthDate.hashCode;
  }
}
