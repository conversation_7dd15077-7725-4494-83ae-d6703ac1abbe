import 'package:flutter/material.dart';
import '../models/post.dart';

class CommentStatsWidget extends StatelessWidget {
  final Post post;
  final VoidCallback? onTap;

  const CommentStatsWidget({
    Key? key,
    required this.post,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final totalLikes = post.comments.fold<int>(
      0, 
      (sum, comment) => sum + comment.likesCount,
    );

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: Colors.grey.shade50,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey.shade200),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // أيقونة التعليقات
            Icon(
              Icons.comment_outlined,
              size: 18,
              color: Colors.blue.shade600,
            ),
            const SizedBox(width: 6),
            Text(
              '${post.comments.length}',
              style: TextStyle(
                color: Colors.blue.shade700,
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
            
            // فاصل إذا كان هناك إعجابات
            if (totalLikes > 0) ...[
              const SizedBox(width: 12),
              Container(
                width: 1,
                height: 16,
                color: Colors.grey.shade300,
              ),
              const SizedBox(width: 12),
              
              // أيقونة الإعجابات
              Icon(
                Icons.favorite,
                size: 16,
                color: Colors.red.shade500,
              ),
              const SizedBox(width: 4),
              Text(
                '$totalLikes',
                style: TextStyle(
                  color: Colors.red.shade600,
                  fontSize: 13,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
            
            // نص توضيحي
            const SizedBox(width: 8),
            Text(
              totalLikes > 0 ? 'تفاعل' : 'تعليق',
              style: TextStyle(
                color: Colors.grey.shade600,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// ويدجت مبسط للإحصائيات
class SimpleCommentStats extends StatelessWidget {
  final Post post;
  final VoidCallback? onTap;

  const SimpleCommentStats({
    Key? key,
    required this.post,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final totalLikes = post.comments.fold<int>(
      0, 
      (sum, comment) => sum + comment.likesCount,
    );

    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.blue.shade50,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.comment,
              size: 14,
              color: Colors.blue.shade600,
            ),
            const SizedBox(width: 4),
            Text(
              '${post.comments.length}',
              style: TextStyle(
                color: Colors.blue.shade700,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
            if (totalLikes > 0) ...[
              const SizedBox(width: 6),
              Icon(
                Icons.favorite,
                size: 12,
                color: Colors.red.shade500,
              ),
              const SizedBox(width: 2),
              Text(
                '$totalLikes',
                style: TextStyle(
                  color: Colors.red.shade600,
                  fontSize: 11,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

// ويدجت للإحصائيات التفصيلية
class DetailedCommentStats extends StatelessWidget {
  final Post post;

  const DetailedCommentStats({
    Key? key,
    required this.post,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final totalLikes = post.comments.fold<int>(
      0, 
      (sum, comment) => sum + comment.likesCount,
    );
    
    final mostLikedComment = post.comments.isNotEmpty
        ? post.comments.reduce((a, b) => a.likesCount > b.likesCount ? a : b)
        : null;

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'إحصائيات التعليقات',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade700,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              _buildStatItem(
                icon: Icons.comment,
                label: 'تعليق',
                value: post.comments.length,
                color: Colors.blue,
              ),
              const SizedBox(width: 16),
              _buildStatItem(
                icon: Icons.favorite,
                label: 'إعجاب',
                value: totalLikes,
                color: Colors.red,
              ),
            ],
          ),
          if (mostLikedComment != null && mostLikedComment.likesCount > 0) ...[
            const SizedBox(height: 8),
            Text(
              'أكثر تعليق إعجاباً: ${mostLikedComment.likesCount} إعجاب',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade600,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildStatItem({
    required IconData icon,
    required String label,
    required int value,
    required MaterialColor color,
  }) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          icon,
          size: 16,
          color: color.shade600,
        ),
        const SizedBox(width: 4),
        Text(
          '$value',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: color.shade700,
          ),
        ),
        const SizedBox(width: 2),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade600,
          ),
        ),
      ],
    );
  }
}
