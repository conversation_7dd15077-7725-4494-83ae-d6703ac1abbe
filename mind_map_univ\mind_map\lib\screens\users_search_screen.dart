import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/auth_provider.dart';
import '../models/user_model.dart';
import '../services/restriction_service.dart';
import '../widgets/restriction_dialog.dart';
import 'user_profile_screen.dart';

class UsersSearchScreen extends StatefulWidget {
  const UsersSearchScreen({super.key});

  @override
  State<UsersSearchScreen> createState() => _UsersSearchScreenState();
}

class _UsersSearchScreenState extends State<UsersSearchScreen> {
  final TextEditingController _searchController = TextEditingController();
  List<UserModel> _searchResults = [];
  bool _isLoading = false;
  String _selectedUniversity = '';

  @override
  void initState() {
    super.initState();
    _loadInitialUsers();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadInitialUsers() async {
    setState(() => _isLoading = true);

    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final currentUser = authProvider.userModel;

    print('🔄 تحميل المستخدمين الأولي...');
    print('👤 المستخدم الحالي: ${currentUser?.email}');
    print('🏫 جامعة المستخدم: ${currentUser?.university}');

    // تحميل جميع المستخدمين أولاً
    final users = await authProvider.searchUsers(
      limit: 50,
    );

    print('📊 تم تحميل ${users.length} مستخدم');

    setState(() {
      _searchResults = users;
      _selectedUniversity = ''; // ابدأ بجميع الجامعات
      _isLoading = false;
    });
  }

  Future<void> _searchUsers() async {
    setState(() => _isLoading = true);

    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final searchQuery = _searchController.text.trim();

    print('🔍 بحث جديد...');
    print('📝 النص: "$searchQuery"');
    print('🏫 الجامعة: "$_selectedUniversity"');

    final users = await authProvider.searchUsers(
      query: searchQuery.isEmpty ? null : searchQuery,
      university: _selectedUniversity.isEmpty ? null : _selectedUniversity,
      limit: 50,
    );

    print('✅ نتائج البحث: ${users.length} مستخدم');

    setState(() {
      _searchResults = users;
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('البحث عن المستخدمين'),
        backgroundColor: Colors.blue.shade700,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Column(
        children: [
          // شريط البحث والفلاتر
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.blue.shade700,
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(20),
                bottomRight: Radius.circular(20),
              ),
            ),
            child: Column(
              children: [
                // حقل البحث
                TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'ابحث بالاسم، الإيميل، الجامعة، أو التخصص...',
                    prefixIcon: const Icon(Icons.search),
                    suffixIcon: IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        _searchController.clear();
                        _searchUsers();
                      },
                    ),
                    filled: true,
                    fillColor: Colors.white,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide.none,
                    ),
                  ),
                  onSubmitted: (_) => _searchUsers(),
                ),
                const SizedBox(height: 12),
                
                // فلتر الجامعة
                Row(
                  children: [
                    const Icon(Icons.school, color: Colors.white, size: 20),
                    const SizedBox(width: 8),
                    const Text(
                      'الجامعة:',
                      style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: DropdownButtonHideUnderline(
                          child: DropdownButton<String>(
                            value: _selectedUniversity.isEmpty ? null : _selectedUniversity,
                            hint: const Text('جميع الجامعات'),
                            isExpanded: true,
                            items: [
                              const DropdownMenuItem<String>(
                                value: '',
                                child: Text('جميع الجامعات'),
                              ),
                              DropdownMenuItem<String>(
                                value: Provider.of<AuthProvider>(context, listen: false).userModel?.university ?? '',
                                child: Text(Provider.of<AuthProvider>(context, listen: false).userModel?.university ?? 'جامعتي'),
                              ),
                            ],
                            onChanged: (value) {
                              setState(() {
                                _selectedUniversity = value ?? '';
                              });
                              _searchUsers();
                            },
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          
          // النتائج
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _searchResults.isEmpty
                    ? const Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.people_outline, size: 64, color: Colors.grey),
                            SizedBox(height: 16),
                            Text(
                              'لا توجد نتائج',
                              style: TextStyle(fontSize: 18, color: Colors.grey),
                            ),
                            SizedBox(height: 8),
                            Text(
                              'جرب البحث بكلمات مختلفة',
                              style: TextStyle(color: Colors.grey),
                            ),
                          ],
                        ),
                      )
                    : ListView.builder(
                        padding: const EdgeInsets.all(16),
                        itemCount: _searchResults.length,
                        itemBuilder: (context, index) {
                          final user = _searchResults[index];
                          return _buildUserCard(user);
                        },
                      ),
          ),
        ],
      ),
    );
  }

  Widget _buildUserCard(UserModel user) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        final currentUser = authProvider.userModel;
        final isFollowing = currentUser?.isFollowing(user.uid) ?? false;
        
        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          elevation: 4,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          child: InkWell(
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => UserProfileScreen(user: user),
                ),
              );
            },
            borderRadius: BorderRadius.circular(12),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  // صورة المستخدم
                  CircleAvatar(
                    radius: 30,
                    backgroundColor: Colors.blue.shade100,
                    child: Text(
                      _getAuthorInitial(user.fullName),
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.blue.shade700,
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  
                  // معلومات المستخدم
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          user.fullName,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          user.university,
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey.shade600,
                          ),
                        ),
                        Text(
                          user.major,
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey.shade600,
                          ),
                        ),
                        const SizedBox(height: 8),
                        
                        // إحصائيات المتابعة (حسب إعدادات الخصوصية)
                        Consumer<AuthProvider>(
                          builder: (context, authProvider, child) {
                            final currentUserId = authProvider.user?.uid;
                            final canViewFollowers = user.canViewFollowers(currentUserId);
                            final canViewFollowing = user.canViewFollowing(currentUserId);

                            return Wrap(
                              spacing: 8,
                              runSpacing: 4,
                              children: [
                                // عرض المتابعين فقط إذا كان مسموحاً
                                if (canViewFollowers)
                                  _buildStatChip('${user.followersCount} متابع', Icons.people),
                                // عرض المتابَعين فقط إذا كان مسموحاً
                                if (canViewFollowing)
                                  _buildStatChip('${user.followingCount} متابَع', Icons.person_add),
                                // حالة النشاط
                                Container(
                                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                  decoration: BoxDecoration(
                                    color: user.isActive ? Colors.green.shade50 : Colors.grey.shade50,
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Container(
                                        width: 6,
                                        height: 6,
                                        decoration: BoxDecoration(
                                          color: user.isActive ? Colors.green : Colors.grey,
                                          shape: BoxShape.circle,
                                        ),
                                      ),
                                      const SizedBox(width: 4),
                                      Text(
                                        user.isActive ? 'نشط' : 'غير نشط',
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: user.isActive ? Colors.green.shade700 : Colors.grey.shade600,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            );
                          },
                        ),
                      ],
                    ),
                  ),
                  
                  // زر المتابعة (إذا كان يسمح بالمتابعة أو متابع بالفعل)
                  if ((user.allowFollowing ?? true) || isFollowing)
                    ElevatedButton(
                      onPressed: authProvider.isLoading
                          ? null
                          : (isFollowing || (user.allowFollowing ?? true))
                              ? () async {
                                  if (isFollowing) {
                                    await authProvider.unfollowUser(user.uid);
                                  } else {
                                    // التحقق من تقييد المتابعة
                                    final currentUser = authProvider.userModel;
                                    if (currentUser != null) {
                                      final restriction = await RestrictionService.checkFollowingRestriction(currentUser.uid);
                                      if (restriction != null && mounted) {
                                        await RestrictionDialog.show(context, restriction, 'متابعة المستخدمين');
                                        return;
                                      }
                                    }
                                    await authProvider.followUser(user.uid);
                                  }
                                }
                              : null, // تعطيل الزر إذا كان لا يسمح بالمتابعة وليس متابعاً
                      style: ElevatedButton.styleFrom(
                        backgroundColor: isFollowing ? Colors.grey.shade300 : Colors.blue.shade700,
                        foregroundColor: isFollowing ? Colors.black87 : Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20),
                        ),
                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      ),
                      child: Text(
                        isFollowing
                            ? 'إلغاء المتابعة'
                            : (user.allowFollowing ?? true)
                                ? 'متابعة'
                                : 'لا يسمح بالمتابعة',
                        style: const TextStyle(fontSize: 12),
                      ),
                    )
                  else
                    // رسالة عدم السماح بالمتابعة
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: Colors.grey.shade200,
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(color: Colors.grey.shade400),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(Icons.lock, size: 14, color: Colors.grey.shade600),
                          const SizedBox(width: 4),
                          Text(
                            'لا يسمح بالمتابعة',
                            style: TextStyle(
                              fontSize: 11,
                              color: Colors.grey.shade600,
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildStatChip(String text, IconData icon) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: Colors.blue.shade700),
          const SizedBox(width: 4),
          Text(
            text,
            style: TextStyle(
              fontSize: 12,
              color: Colors.blue.shade700,
            ),
          ),
        ],
      ),
    );
  }

  String _getAuthorInitial(String authorName) {
    if (authorName.trim().isEmpty) {
      return 'م'; // مستخدم
    }

    // إزالة المسافات الزائدة والحصول على الحرف الأول
    final trimmedName = authorName.trim();
    return trimmedName[0].toUpperCase();
  }
}
