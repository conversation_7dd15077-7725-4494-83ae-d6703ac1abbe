class AdminStatistics {
  // إحصائيات المستخدمين
  final int totalUsers;
  final int activeUsersToday;
  final int activeUsersThisWeek;
  final int activeUsersThisMonth;
  final int newUsersToday;
  final int newUsersThisWeek;
  final int newUsersThisMonth;
  final Map<String, int> usersByUniversity;
  final Map<String, int> usersByMajor;
  final Map<String, int> usersByAge;

  // إحصائيات المخططات الذهنية
  final int totalMindMaps;
  final int mindMapsCreatedToday;
  final int mindMapsCreatedThisWeek;
  final int mindMapsCreatedThisMonth;
  final Map<String, int> mindMapsBySubject;
  final int favoriteMindMaps;
  final int publishedMindMaps;
  final int templatedMindMaps;

  // إحصائيات المنشورات
  final int totalPosts;
  final int postsCreatedToday;
  final int postsCreatedThisWeek;
  final int postsCreatedThisMonth;
  final int totalComments;
  final int totalReactions;
  final Map<String, int> postsBySubject;

  // إحصائيات طلبات التعديل
  final int totalEditRequests;
  final int pendingEditRequests;
  final int approvedEditRequests;
  final int rejectedEditRequests;
  final int editRequestsToday;
  final int editRequestsThisWeek;

  // إحصائيات القوالب
  final int totalTemplates;
  final int customTemplates;
  final int predefinedTemplates;
  final Map<String, int> templatesByCategory;

  // إحصائيات التفاعل
  final int totalFollowRelations;
  final double averageFollowersPerUser;
  final double averageFollowingPerUser;
  final double averagePostsPerUser;
  final double averageMindMapsPerUser;

  const AdminStatistics({
    required this.totalUsers,
    required this.activeUsersToday,
    required this.activeUsersThisWeek,
    required this.activeUsersThisMonth,
    required this.newUsersToday,
    required this.newUsersThisWeek,
    required this.newUsersThisMonth,
    required this.usersByUniversity,
    required this.usersByMajor,
    required this.usersByAge,
    required this.totalMindMaps,
    required this.mindMapsCreatedToday,
    required this.mindMapsCreatedThisWeek,
    required this.mindMapsCreatedThisMonth,
    required this.mindMapsBySubject,
    required this.favoriteMindMaps,
    required this.publishedMindMaps,
    required this.templatedMindMaps,
    required this.totalPosts,
    required this.postsCreatedToday,
    required this.postsCreatedThisWeek,
    required this.postsCreatedThisMonth,
    required this.totalComments,
    required this.totalReactions,
    required this.postsBySubject,
    required this.totalEditRequests,
    required this.pendingEditRequests,
    required this.approvedEditRequests,
    required this.rejectedEditRequests,
    required this.editRequestsToday,
    required this.editRequestsThisWeek,
    required this.totalTemplates,
    required this.customTemplates,
    required this.predefinedTemplates,
    required this.templatesByCategory,
    required this.totalFollowRelations,
    required this.averageFollowersPerUser,
    required this.averageFollowingPerUser,
    required this.averagePostsPerUser,
    required this.averageMindMapsPerUser,
  });

  // إنشاء إحصائيات فارغة
  factory AdminStatistics.empty() {
    return const AdminStatistics(
      totalUsers: 0,
      activeUsersToday: 0,
      activeUsersThisWeek: 0,
      activeUsersThisMonth: 0,
      newUsersToday: 0,
      newUsersThisWeek: 0,
      newUsersThisMonth: 0,
      usersByUniversity: {},
      usersByMajor: {},
      usersByAge: {},
      totalMindMaps: 0,
      mindMapsCreatedToday: 0,
      mindMapsCreatedThisWeek: 0,
      mindMapsCreatedThisMonth: 0,
      mindMapsBySubject: {},
      favoriteMindMaps: 0,
      publishedMindMaps: 0,
      templatedMindMaps: 0,
      totalPosts: 0,
      postsCreatedToday: 0,
      postsCreatedThisWeek: 0,
      postsCreatedThisMonth: 0,
      totalComments: 0,
      totalReactions: 0,
      postsBySubject: {},
      totalEditRequests: 0,
      pendingEditRequests: 0,
      approvedEditRequests: 0,
      rejectedEditRequests: 0,
      editRequestsToday: 0,
      editRequestsThisWeek: 0,
      totalTemplates: 0,
      customTemplates: 0,
      predefinedTemplates: 0,
      templatesByCategory: {},
      totalFollowRelations: 0,
      averageFollowersPerUser: 0.0,
      averageFollowingPerUser: 0.0,
      averagePostsPerUser: 0.0,
      averageMindMapsPerUser: 0.0,
    );
  }

  // تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'totalUsers': totalUsers,
      'activeUsersToday': activeUsersToday,
      'activeUsersThisWeek': activeUsersThisWeek,
      'activeUsersThisMonth': activeUsersThisMonth,
      'newUsersToday': newUsersToday,
      'newUsersThisWeek': newUsersThisWeek,
      'newUsersThisMonth': newUsersThisMonth,
      'usersByUniversity': usersByUniversity,
      'usersByMajor': usersByMajor,
      'usersByAge': usersByAge,
      'totalMindMaps': totalMindMaps,
      'mindMapsCreatedToday': mindMapsCreatedToday,
      'mindMapsCreatedThisWeek': mindMapsCreatedThisWeek,
      'mindMapsCreatedThisMonth': mindMapsCreatedThisMonth,
      'mindMapsBySubject': mindMapsBySubject,
      'favoriteMindMaps': favoriteMindMaps,
      'publishedMindMaps': publishedMindMaps,
      'templatedMindMaps': templatedMindMaps,
      'totalPosts': totalPosts,
      'postsCreatedToday': postsCreatedToday,
      'postsCreatedThisWeek': postsCreatedThisWeek,
      'postsCreatedThisMonth': postsCreatedThisMonth,
      'totalComments': totalComments,
      'totalReactions': totalReactions,
      'postsBySubject': postsBySubject,
      'totalEditRequests': totalEditRequests,
      'pendingEditRequests': pendingEditRequests,
      'approvedEditRequests': approvedEditRequests,
      'rejectedEditRequests': rejectedEditRequests,
      'editRequestsToday': editRequestsToday,
      'editRequestsThisWeek': editRequestsThisWeek,
      'totalTemplates': totalTemplates,
      'customTemplates': customTemplates,
      'predefinedTemplates': predefinedTemplates,
      'templatesByCategory': templatesByCategory,
      'totalFollowRelations': totalFollowRelations,
      'averageFollowersPerUser': averageFollowersPerUser,
      'averageFollowingPerUser': averageFollowingPerUser,
      'averagePostsPerUser': averagePostsPerUser,
      'averageMindMapsPerUser': averageMindMapsPerUser,
    };
  }

  // إنشاء من JSON
  factory AdminStatistics.fromJson(Map<String, dynamic> json) {
    return AdminStatistics(
      totalUsers: json['totalUsers'] ?? 0,
      activeUsersToday: json['activeUsersToday'] ?? 0,
      activeUsersThisWeek: json['activeUsersThisWeek'] ?? 0,
      activeUsersThisMonth: json['activeUsersThisMonth'] ?? 0,
      newUsersToday: json['newUsersToday'] ?? 0,
      newUsersThisWeek: json['newUsersThisWeek'] ?? 0,
      newUsersThisMonth: json['newUsersThisMonth'] ?? 0,
      usersByUniversity: Map<String, int>.from(json['usersByUniversity'] ?? {}),
      usersByMajor: Map<String, int>.from(json['usersByMajor'] ?? {}),
      usersByAge: Map<String, int>.from(json['usersByAge'] ?? {}),
      totalMindMaps: json['totalMindMaps'] ?? 0,
      mindMapsCreatedToday: json['mindMapsCreatedToday'] ?? 0,
      mindMapsCreatedThisWeek: json['mindMapsCreatedThisWeek'] ?? 0,
      mindMapsCreatedThisMonth: json['mindMapsCreatedThisMonth'] ?? 0,
      mindMapsBySubject: Map<String, int>.from(json['mindMapsBySubject'] ?? {}),
      favoriteMindMaps: json['favoriteMindMaps'] ?? 0,
      publishedMindMaps: json['publishedMindMaps'] ?? 0,
      templatedMindMaps: json['templatedMindMaps'] ?? 0,
      totalPosts: json['totalPosts'] ?? 0,
      postsCreatedToday: json['postsCreatedToday'] ?? 0,
      postsCreatedThisWeek: json['postsCreatedThisWeek'] ?? 0,
      postsCreatedThisMonth: json['postsCreatedThisMonth'] ?? 0,
      totalComments: json['totalComments'] ?? 0,
      totalReactions: json['totalReactions'] ?? 0,
      postsBySubject: Map<String, int>.from(json['postsBySubject'] ?? {}),
      totalEditRequests: json['totalEditRequests'] ?? 0,
      pendingEditRequests: json['pendingEditRequests'] ?? 0,
      approvedEditRequests: json['approvedEditRequests'] ?? 0,
      rejectedEditRequests: json['rejectedEditRequests'] ?? 0,
      editRequestsToday: json['editRequestsToday'] ?? 0,
      editRequestsThisWeek: json['editRequestsThisWeek'] ?? 0,
      totalTemplates: json['totalTemplates'] ?? 0,
      customTemplates: json['customTemplates'] ?? 0,
      predefinedTemplates: json['predefinedTemplates'] ?? 0,
      templatesByCategory: Map<String, int>.from(json['templatesByCategory'] ?? {}),
      totalFollowRelations: json['totalFollowRelations'] ?? 0,
      averageFollowersPerUser: (json['averageFollowersPerUser'] ?? 0.0).toDouble(),
      averageFollowingPerUser: (json['averageFollowingPerUser'] ?? 0.0).toDouble(),
      averagePostsPerUser: (json['averagePostsPerUser'] ?? 0.0).toDouble(),
      averageMindMapsPerUser: (json['averageMindMapsPerUser'] ?? 0.0).toDouble(),
    );
  }
}
