rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // قواعد لملفات المستخدمين - يمكن للمستخدم رفع وقراءة ملفاته فقط
    match /users/{userId}/{allPaths=**} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // قواعد لصور المخططات الذهنية
    match /mindMaps/{userId}/{allPaths=**} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // قواعد للقوالب العامة - يمكن للجميع قراءتها
    match /templates/{allPaths=**} {
      allow read: if true;
      allow write: if request.auth != null;
    }
    
    // منع الوصول لأي ملفات أخرى
    match /{allPaths=**} {
      allow read, write: if false;
    }
  }
}
