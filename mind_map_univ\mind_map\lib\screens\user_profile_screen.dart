import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/auth_provider.dart';
import '../models/user_model.dart';
import '../services/restriction_service.dart';
import '../widgets/restriction_dialog.dart';

class UserProfileScreen extends StatefulWidget {
  final UserModel user;

  const UserProfileScreen({
    super.key,
    required this.user,
  });

  @override
  State<UserProfileScreen> createState() => _UserProfileScreenState();
}

class _UserProfileScreenState extends State<UserProfileScreen> {
  @override
  void initState() {
    super.initState();
    // تحديث آخر نشاط عند فتح الشاشة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      authProvider.updateLastActive();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        final currentUser = authProvider.userModel;
        final isFollowing = currentUser?.isFollowing(widget.user.uid) ?? false;

        // التحقق من إعدادات الخصوصية
        final isProfilePublic = widget.user.isProfilePublic ?? true;

        // إذا كان الملف الشخصي مخفي وليس المستخدم الحالي
        if (!isProfilePublic && currentUser?.uid != widget.user.uid) {
          return Scaffold(
            appBar: AppBar(
              title: const Text('ملف شخصي'),
              backgroundColor: Colors.blue.shade700,
              foregroundColor: Colors.white,
            ),
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.lock,
                    size: 80,
                    color: Colors.grey.shade400,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'الملف الشخصي مخفي',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey.shade600,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'هذا المستخدم قام بإخفاء ملفه الشخصي',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey.shade500,
                    ),
                  ),
                ],
              ),
            ),
          );
        }

        return Scaffold(
          body: CustomScrollView(
            slivers: [
              // AppBar مع تأثير التمرير
              SliverAppBar(
                expandedHeight: 180,
                floating: false,
                pinned: true,
                backgroundColor: Colors.blue.shade700,
                foregroundColor: Colors.white,
                title: Text(widget.user.fullName),
                flexibleSpace: FlexibleSpaceBar(
                  background: Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.blue.shade700,
                          Colors.blue.shade500,
                          Colors.blue.shade300,
                        ],
                        stops: const [0.0, 0.7, 1.0],
                      ),
                    ),
                  ),
                ),
                actions: [
                  // زر المتابعة في الـ AppBar (إذا كان يسمح بالمتابعة أو متابع بالفعل)
                  if ((widget.user.allowFollowing ?? true) || isFollowing)
                    Padding(
                      padding: const EdgeInsets.only(right: 16),
                      child: ElevatedButton(
                        onPressed: authProvider.isLoading
                            ? null
                            : (isFollowing || (widget.user.allowFollowing ?? true))
                                ? () async {
                                    if (isFollowing) {
                                      await authProvider.unfollowUser(widget.user.uid);
                                    } else {
                                      // التحقق من تقييد المتابعة
                                      final currentUser = authProvider.userModel;
                                      if (currentUser != null) {
                                        final restriction = await RestrictionService.checkFollowingRestriction(currentUser.uid);
                                        if (restriction != null && mounted) {
                                          await RestrictionDialog.show(context, restriction, 'متابعة المستخدمين');
                                          return;
                                        }
                                      }
                                      await authProvider.followUser(widget.user.uid);
                                    }
                                  }
                                : null, // تعطيل الزر إذا كان لا يسمح بالمتابعة وليس متابعاً
                        style: ElevatedButton.styleFrom(
                          backgroundColor: isFollowing ? Colors.white : Colors.blue.shade900,
                          foregroundColor: isFollowing ? Colors.blue.shade700 : Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(20),
                          ),
                          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                        ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            isFollowing ? Icons.person_remove : Icons.person_add,
                            size: 16,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            isFollowing
                                ? 'إلغاء المتابعة'
                                : (widget.user.allowFollowing ?? true)
                                    ? 'متابعة'
                                    : 'لا يسمح بالمتابعة',
                            style: const TextStyle(fontSize: 12),
                          ),
                        ],
                      ),
                    ),
                  )
                  else
                    // رسالة عدم السماح بالمتابعة
                    Padding(
                      padding: const EdgeInsets.only(right: 16),
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                        decoration: BoxDecoration(
                          color: Colors.grey.shade200,
                          borderRadius: BorderRadius.circular(20),
                          border: Border.all(color: Colors.grey.shade400),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(Icons.lock, size: 16, color: Colors.grey.shade600),
                            const SizedBox(width: 6),
                            Text(
                              'لا يسمح بالمتابعة',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey.shade600,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                ],
              ),
              
              // محتوى الشاشة
              SliverToBoxAdapter(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    children: [
                      // بطاقة المعلومات الأساسية
                      _buildMainInfoCard(),
                      const SizedBox(height: 16),
                      
                      // إحصائيات المتابعة
                      _buildFollowStatsCard(),
                      const SizedBox(height: 16),
                      
                      // المعلومات الأكاديمية
                      _buildAcademicInfoCard(),
                      
                      // النبذة الشخصية
                      if (widget.user.bio.isNotEmpty) ...[
                        const SizedBox(height: 16),
                        _buildBioCard(),
                      ],
                      
                      const SizedBox(height: 16),
                      
                      // معلومات إضافية
                      _buildAdditionalInfoCard(),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildMainInfoCard() {
    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.white,
              Colors.blue.shade50,
            ],
          ),
        ),
        child: Column(
          children: [
            // صورة المستخدم
            CircleAvatar(
              radius: 50,
              backgroundColor: Colors.blue.shade100,
              child: Text(
                _getAuthorInitial(widget.user.fullName),
                style: TextStyle(
                  fontSize: 36,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue.shade700,
                ),
              ),
            ),
            const SizedBox(height: 16),
            
            // اسم المستخدم
            Text(
              widget.user.fullName,
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 8),
            
            // الإيميل (حسب إعدادات الخصوصية)
            if (widget.user.showEmail ?? false)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.blue.shade100,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(Icons.email, size: 16, color: Colors.blue.shade700),
                    const SizedBox(width: 8),
                    Text(
                      widget.user.email,
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.blue.shade700,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            const SizedBox(height: 12),
            
            // حالة النشاط
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: widget.user.isActive ? Colors.green.shade50 : Colors.grey.shade50,
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: widget.user.isActive ? Colors.green.shade200 : Colors.grey.shade200,
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    width: 8,
                    height: 8,
                    decoration: BoxDecoration(
                      color: widget.user.isActive ? Colors.green : Colors.grey,
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    widget.user.isActive 
                        ? 'نشط الآن' 
                        : 'غير نشط منذ ${widget.user.inactiveTime}',
                    style: TextStyle(
                      fontSize: 14,
                      color: widget.user.isActive ? Colors.green.shade700 : Colors.grey.shade600,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFollowStatsCard() {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        final currentUserId = authProvider.user?.uid;

        // التحقق من إعدادات الخصوصية
        final canViewFollowers = widget.user.canViewFollowers(currentUserId);
        final canViewFollowing = widget.user.canViewFollowing(currentUserId);

        // إذا كانت جميع الإحصائيات مخفية، لا تعرض البطاقة
        if (!canViewFollowers && !canViewFollowing) {
          return const SizedBox.shrink();
        }

        return Card(
          elevation: 4,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                // عرض المتابعين فقط إذا كان مسموحاً
                if (canViewFollowers) ...[
                  _buildStatItem(
                    icon: Icons.people,
                    count: widget.user.followersCount,
                    label: 'متابع',
                    color: Colors.blue,
                  ),
                  if (canViewFollowing) // إضافة الفاصل فقط إذا كان هناك عنصر آخر
                    Container(
                      width: 1,
                      height: 40,
                      color: Colors.grey.shade300,
                    ),
                ],
                // عرض المتابَعين فقط إذا كان مسموحاً
                if (canViewFollowing)
                  _buildStatItem(
                    icon: Icons.person_add,
                    count: widget.user.followingCount,
                    label: 'متابَع',
                    color: Colors.green,
                  ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildStatItem({
    required IconData icon,
    required int count,
    required String label,
    required Color color,
  }) {
    return Column(
      children: [
        Icon(icon, color: color, size: 28),
        const SizedBox(height: 8),
        Text(
          count.toString(),
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey.shade600,
          ),
        ),
      ],
    );
  }

  Widget _buildAcademicInfoCard() {
    // التحقق من إعدادات الخصوصية
    final showUniversity = widget.user.showUniversity ?? true;
    final showMajor = widget.user.showMajor ?? true;

    // إذا كانت جميع المعلومات مخفية، لا تعرض البطاقة
    if (!showUniversity && !showMajor) {
      return const SizedBox.shrink();
    }

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.school, color: Colors.blue.shade700, size: 24),
                const SizedBox(width: 12),
                const Text(
                  'المعلومات الأكاديمية',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // عرض الجامعة فقط إذا كانت مسموحة
            if (showUniversity) ...[
              _buildInfoRow(Icons.account_balance, 'الجامعة', widget.user.university),
              if (showMajor) const SizedBox(height: 12),
            ],
            // عرض التخصص فقط إذا كان مسموحاً
            if (showMajor) ...[
              _buildInfoRow(Icons.book, 'التخصص', widget.user.major),
              const SizedBox(height: 12),
            ],
            // العمر يظهر دائماً
            _buildInfoRow(Icons.cake, 'العمر', '${widget.user.age} سنة'),
          ],
        ),
      ),
    );
  }

  Widget _buildBioCard() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info_outline, color: Colors.blue.shade700, size: 24),
                const SizedBox(width: 12),
                const Text(
                  'النبذة الشخصية',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              widget.user.bio,
              style: const TextStyle(
                fontSize: 16,
                height: 1.5,
                color: Colors.black87,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAdditionalInfoCard() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.access_time, color: Colors.blue.shade700, size: 24),
                const SizedBox(width: 12),
                const Text(
                  'معلومات إضافية',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            _buildInfoRow(
              Icons.calendar_today,
              'تاريخ الانضمام',
              '${widget.user.createdAt.day}/${widget.user.createdAt.month}/${widget.user.createdAt.year}',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Row(
      children: [
        Icon(icon, size: 20, color: Colors.grey.shade600),
        const SizedBox(width: 12),
        Text(
          '$label: ',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: Colors.grey.shade700,
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(
              fontSize: 16,
              color: Colors.black87,
            ),
          ),
        ),
      ],
    );
  }

  String _getAuthorInitial(String authorName) {
    if (authorName.trim().isEmpty) {
      return 'م'; // مستخدم
    }

    // إزالة المسافات الزائدة والحصول على الحرف الأول
    final trimmedName = authorName.trim();
    return trimmedName[0].toUpperCase();
  }
}
