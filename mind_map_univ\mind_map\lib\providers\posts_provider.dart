import 'package:flutter/material.dart';
import 'package:firebase_database/firebase_database.dart';
import '../models/post.dart';
import '../models/reaction.dart';
import '../models/mind_map.dart';
import '../models/user_model.dart';


class PostsProvider extends ChangeNotifier {
  final FirebaseDatabase _database = FirebaseDatabase.instance;

  // Callbacks للإشعارات
  Function(List<String> followerIds, UserModel author, String postId, String postTitle, PostAudience audience)? onNewPost;
  Function(String postAuthorId, UserModel reactor, String postId, String postTitle, ReactionType reactionType)? onPostReacted;
  Function(String postAuthorId, UserModel commenter, String postId, String postTitle, String commentId)? onPostCommented;
  Function(String commentAuthorId, UserModel replier, String postId, String commentId)? onCommentReplied;
  Function(String commentAuthorId, UserModel reactor, String postId, String commentId, ReactionType reactionType)? onCommentReacted;
  
  List<Post> _posts = [];
  bool _isLoading = false;
  String? _error;
  String? _currentUserId;

  // Getters
  List<Post> get posts => List.unmodifiable(_posts);
  bool get isLoading => _isLoading;
  String? get error => _error;

  // تعيين المستخدم الحالي
  void setCurrentUser(String? userId) {
    if (_currentUserId != userId) {
      _currentUserId = userId;
      _posts.clear();
      if (userId != null) {
        loadPosts();
      }
      notifyListeners();
    }
  }

  // تحميل المنشورات
  Future<void> loadPosts() async {
    _setLoading(true);
    _setError(null);

    try {
      print('🔄 تحميل المنشورات من Firebase...');

      final postsRef = _database.ref('posts');
      final snapshot = await postsRef.once();

      _posts = [];
      if (snapshot.snapshot.exists && snapshot.snapshot.value != null) {
        final postsData = Map<String, dynamic>.from(snapshot.snapshot.value as Map);

        // تحميل معلومات المستخدم الحالي للفلترة
        UserModel? currentUser;
        if (_currentUserId != null) {
          currentUser = await _getCurrentUserData(_currentUserId!);
        }

        for (var entry in postsData.entries) {
          try {
            final postData = _convertToStringDynamicMap(entry.value);
            final post = Post.fromJson(postData);

            // فلترة المنشورات حسب الجمهور
            if (await _canViewPost(post, currentUser)) {
              _posts.add(post);
            }
          } catch (e) {
            print('❌ خطأ في تحويل منشور: $e');
          }
        }

        // ترتيب المنشورات حسب التاريخ (الأحدث أولاً)
        _posts.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        print('✅ تم تحميل ${_posts.length} منشور (بعد الفلترة)');
      } else {
        print('⚠️ لا توجد منشورات في Firebase');
      }
    } catch (e) {
      print('❌ خطأ في تحميل المنشورات: $e');
      _setError('خطأ في تحميل المنشورات: $e');
    } finally {
      _setLoading(false);
    }
  }

  // إنشاء منشور جديد
  Future<Post> createPost({
    required MindMap mindMap,
    required UserModel author,
    String description = '',
    PostAudience audience = PostAudience.everyone,
  }) async {
    try {
      print('📝 إنشاء منشور جديد للمخطط: ${mindMap.title}');

      final post = Post(
        authorId: author.uid,
        authorName: '${author.firstName} ${author.lastName}',
        authorUniversity: author.university,
        mindMapId: mindMap.id,
        mindMapTitle: mindMap.title,
        mindMapSubject: mindMap.subject,
        description: description,
        audience: audience,
        mindMapData: mindMap.toJson(),
      );

      // حفظ المنشور في Firebase
      final postRef = _database.ref('posts/${post.id}');
      await postRef.set(post.toJson());

      // إضافة المنشور للقائمة المحلية
      _posts.insert(0, post);
      notifyListeners();

      print('✅ تم إنشاء المنشور بنجاح: ${post.id}');

      // إنشاء إشعارات للمتابعين
      if (onNewPost != null) {
        try {
          // الحصول على قائمة المتابعين من المؤلف
          final followerIds = author.followers;
          if (followerIds.isNotEmpty) {
            onNewPost!(followerIds, author, post.id, post.mindMapTitle, post.audience);
          }
        } catch (e) {
          print('❌ خطأ في إنشاء إشعارات المنشور: $e');
        }
      }

      return post;
    } catch (e) {
      print('❌ خطأ في إنشاء المنشور: $e');
      _setError('خطأ في إنشاء المنشور: $e');
      rethrow;
    }
  }

  // إضافة/إزالة إعجاب (للتوافق مع النسخة القديمة)
  Future<void> toggleLike(String postId, {UserModel? user}) async {
    if (_currentUserId == null) return;
    await togglePostReaction(postId, _currentUserId!, ReactionType.like, user: user);
  }

  // تبديل رد الفعل على المنشور
  Future<void> togglePostReaction(String postId, String userId, ReactionType reactionType, {UserModel? user}) async {
    try {
      final postIndex = _posts.indexWhere((post) => post.id == postId);
      if (postIndex == -1) return;

      final post = _posts[postIndex];
      final currentReaction = post.reactions.getUserReaction(userId);

      ReactionSummary updatedReactions;
      if (currentReaction == reactionType) {
        // إزالة رد الفعل إذا كان نفس النوع
        updatedReactions = post.reactions.removeReaction(userId);
      } else {
        // إضافة أو تغيير رد الفعل
        updatedReactions = post.reactions.addReaction(userId, reactionType);
      }

      final updatedPost = post.copyWith(reactions: updatedReactions);
      _posts[postIndex] = updatedPost;
      notifyListeners();

      // تحديث Firebase
      await _database.ref('posts/${post.id}').update({
        'reactions': updatedReactions.toJson(),
      });

      // إنشاء إشعار رد فعل (فقط عند الإضافة وليس الإزالة)
      if (onPostReacted != null && user != null) {
        // التحقق من أن هذا رد فعل جديد وليس إزالة
        final isNewReaction = currentReaction != reactionType;
        if (isNewReaction) {
          try {
            onPostReacted!(post.authorId, user, post.id, post.mindMapTitle, reactionType);
            print('${reactionType.emoji} تم إرسال إشعار رد الفعل للمنشور: ${post.id}');
          } catch (e) {
            print('❌ خطأ في إرسال إشعار رد الفعل: $e');
          }
        }
      }
    } catch (e) {
      print('❌ خطأ في تبديل رد الفعل: $e');
      rethrow;
    }
  }

  // إضافة تعليق
  Future<void> addComment({
    required String postId,
    required String content,
    required UserModel author,
  }) async {
    try {
      final postIndex = _posts.indexWhere((post) => post.id == postId);
      if (postIndex == -1) return;

      final comment = PostComment(
        authorId: author.uid,
        authorName: '${author.firstName} ${author.lastName}',
        content: content,
      );

      final post = _posts[postIndex];
      final updatedPost = post.addComment(comment);

      // تحديث في Firebase
      final postRef = _database.ref('posts/$postId/comments');
      await postRef.set(updatedPost.comments.map((c) => c.toJson()).toList());

      // تحديث محلياً
      _posts[postIndex] = updatedPost;
      notifyListeners();

      print('💬 تم إضافة تعليق للمنشور: $postId');

      // إنشاء إشعار تعليق
      if (onPostCommented != null) {
        try {
          final post = _posts[postIndex];
          onPostCommented!(post.authorId, author, post.id, post.mindMapTitle, comment.id);
          print('💬 تم إرسال إشعار التعليق');
        } catch (e) {
          print('❌ خطأ في إرسال إشعار التعليق: $e');
        }
      }
    } catch (e) {
      print('❌ خطأ في إضافة التعليق: $e');
      _setError('خطأ في إضافة التعليق: $e');
    }
  }

  // تحديث تعليق
  Future<void> updateComment({
    required String postId,
    required String commentId,
    required String newContent,
    required String userId,
  }) async {
    try {
      final postIndex = _posts.indexWhere((post) => post.id == postId);
      if (postIndex == -1) return;

      final post = _posts[postIndex];
      final commentIndex = post.comments.indexWhere((c) => c.id == commentId);
      if (commentIndex == -1) return;

      final comment = post.comments[commentIndex];
      if (comment.authorId != userId) {
        throw Exception('ليس لديك صلاحية لتعديل هذا التعليق');
      }

      final updatedComment = comment.copyWith(
        content: newContent,
        updatedAt: DateTime.now(),
      );

      final updatedComments = List<PostComment>.from(post.comments);
      updatedComments[commentIndex] = updatedComment;

      final updatedPost = post.copyWith(comments: updatedComments);

      // تحديث في Firebase
      final postRef = _database.ref('posts/$postId/comments');
      await postRef.set(updatedComments.map((c) => c.toJson()).toList());

      // تحديث محلياً
      _posts[postIndex] = updatedPost;
      notifyListeners();

      print('✏️ تم تحديث التعليق: $commentId');
    } catch (e) {
      print('❌ خطأ في تحديث التعليق: $e');
      _setError('خطأ في تحديث التعليق: $e');
      rethrow;
    }
  }

  // حذف تعليق
  Future<void> deleteComment({
    required String postId,
    required String commentId,
    required String userId,
  }) async {
    try {
      final postIndex = _posts.indexWhere((post) => post.id == postId);
      if (postIndex == -1) return;

      final post = _posts[postIndex];
      final comment = post.comments.firstWhere(
        (c) => c.id == commentId,
        orElse: () => throw Exception('التعليق غير موجود'),
      );

      // التحقق من الصلاحية (صاحب التعليق أو صاحب المنشور)
      if (comment.authorId != userId && post.authorId != userId) {
        throw Exception('ليس لديك صلاحية لحذف هذا التعليق');
      }

      final updatedComments = post.comments.where((c) => c.id != commentId).toList();
      final updatedPost = post.copyWith(comments: updatedComments);

      // تحديث في Firebase
      final postRef = _database.ref('posts/$postId/comments');
      await postRef.set(updatedComments.map((c) => c.toJson()).toList());

      // تحديث محلياً
      _posts[postIndex] = updatedPost;
      notifyListeners();

      print('🗑️ تم حذف التعليق: $commentId');
    } catch (e) {
      print('❌ خطأ في حذف التعليق: $e');
      _setError('خطأ في حذف التعليق: $e');
      rethrow;
    }
  }

  // إعجاب/إلغاء إعجاب بتعليق (للتوافق مع النسخة القديمة)
  Future<void> toggleCommentLike({
    required String postId,
    required String commentId,
    required String userId,
    UserModel? user,
  }) async {
    await toggleCommentReaction(postId, commentId, userId, ReactionType.like, user: user);
  }

  // تبديل رد الفعل على التعليق
  Future<void> toggleCommentReaction(String postId, String commentId, String userId, ReactionType reactionType, {UserModel? user}) async {
    try {
      final postIndex = _posts.indexWhere((post) => post.id == postId);
      if (postIndex == -1) return;

      final post = _posts[postIndex];
      final commentIndex = post.comments.indexWhere((c) => c.id == commentId);
      if (commentIndex == -1) return;

      final comment = post.comments[commentIndex];
      final currentReaction = comment.reactions.getUserReaction(userId);

      ReactionSummary updatedReactions;
      if (currentReaction == reactionType) {
        // إزالة رد الفعل إذا كان نفس النوع
        updatedReactions = comment.reactions.removeReaction(userId);
      } else {
        // إضافة أو تغيير رد الفعل
        updatedReactions = comment.reactions.addReaction(userId, reactionType);
      }

      final updatedComment = comment.copyWith(reactions: updatedReactions);
      final updatedComments = List<PostComment>.from(post.comments);
      updatedComments[commentIndex] = updatedComment;

      final updatedPost = post.copyWith(comments: updatedComments);

      // تحديث في Firebase
      await _database.ref('posts/$postId/comments').set(
        updatedComments.map((c) => c.toJson()).toList()
      );

      // تحديث محلياً
      _posts[postIndex] = updatedPost;
      notifyListeners();

      // إنشاء إشعار رد فعل على التعليق (فقط عند الإضافة وليس الإزالة)
      if (onCommentReacted != null && user != null) {
        // التحقق من أن هذا رد فعل جديد وليس إزالة
        final isNewReaction = currentReaction != reactionType;
        if (isNewReaction) {
          try {
            onCommentReacted!(comment.authorId, user, postId, commentId, reactionType);
            print('${reactionType.emoji} تم إرسال إشعار رد الفعل على التعليق: $commentId');
          } catch (e) {
            print('❌ خطأ في إرسال إشعار رد الفعل على التعليق: $e');
          }
        }
      }
    } catch (e) {
      print('❌ خطأ في تبديل رد فعل التعليق: $e');
      rethrow;
    }
  }

  // حذف منشور
  Future<void> deletePost(String postId) async {
    try {
      // حذف من Firebase
      final postRef = _database.ref('posts/$postId');
      await postRef.remove();

      // حذف محلياً
      _posts.removeWhere((post) => post.id == postId);
      notifyListeners();

      print('🗑️ تم حذف المنشور: $postId');
    } catch (e) {
      print('❌ خطأ في حذف المنشور: $e');
      _setError('خطأ في حذف المنشور: $e');
    }
  }

  // الحصول على منشورات مستخدم معين
  List<Post> getPostsByUser(String userId) {
    return _posts.where((post) => post.authorId == userId).toList();
  }

  // البحث في المنشورات
  List<Post> searchPosts(String query) {
    if (query.isEmpty) return _posts;
    
    final lowerQuery = query.toLowerCase();
    return _posts.where((post) {
      return post.mindMapTitle.toLowerCase().contains(lowerQuery) ||
             post.mindMapSubject.toLowerCase().contains(lowerQuery) ||
             post.authorName.toLowerCase().contains(lowerQuery) ||
             post.description.toLowerCase().contains(lowerQuery);
    }).toList();
  }

  // Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  // تحويل البيانات من Firebase إلى Map<String, dynamic>
  Map<String, dynamic> _convertToStringDynamicMap(dynamic data) {
    if (data is Map<String, dynamic>) {
      return data;
    } else if (data is Map) {
      final result = <String, dynamic>{};
      for (var entry in data.entries) {
        final key = entry.key.toString();
        final value = entry.value;

        if (value is Map) {
          result[key] = _convertToStringDynamicMap(value);
        } else if (value is List) {
          result[key] = value.map((item) {
            if (item is Map) {
              return _convertToStringDynamicMap(item);
            }
            return item;
          }).toList();
        } else {
          result[key] = value;
        }
      }
      return result;
    } else {
      throw Exception('البيانات ليست من نوع Map: ${data.runtimeType}');
    }
  }



  // تحديث وصف المنشور
  Future<void> updatePostDescription(String postId, String newDescription) async {
    try {
      // تحديث في Firebase
      await _database.ref('posts/$postId/description').set(newDescription);
      await _database.ref('posts/$postId/updatedAt').set(DateTime.now().toIso8601String());

      // تحديث في القائمة المحلية
      final postIndex = _posts.indexWhere((post) => post.id == postId);
      if (postIndex != -1) {
        _posts[postIndex] = _posts[postIndex].copyWith(
          description: newDescription,
          updatedAt: DateTime.now(),
        );
        notifyListeners();
      }
    } catch (e) {
      debugPrint('خطأ في تحديث وصف المنشور: $e');
      throw Exception('فشل في تحديث المنشور');
    }
  }

  // إضافة رد على تعليق
  Future<void> addReplyToComment({
    required String postId,
    required String parentCommentId,
    required String content,
    required String authorId,
    required String authorName,
    UserModel? user,
  }) async {
    try {
      print('📝 إضافة رد على التعليق: $parentCommentId');

      final reply = PostComment(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        content: content,
        authorId: authorId,
        authorName: authorName,
        createdAt: DateTime.now(),
        parentCommentId: parentCommentId,
      );

      // العثور على المنشور والتعليق الأصلي
      final postIndex = _posts.indexWhere((p) => p.id == postId);
      if (postIndex == -1) {
        throw Exception('المنشور غير موجود');
      }

      final post = _posts[postIndex];
      final commentIndex = post.comments.indexWhere((c) => c.id == parentCommentId);
      if (commentIndex == -1) {
        throw Exception('التعليق غير موجود');
      }

      // إضافة الرد محلياً
      final updatedComment = post.comments[commentIndex].addReply(reply);
      final updatedComments = List<PostComment>.from(post.comments);
      updatedComments[commentIndex] = updatedComment;

      final updatedPost = post.copyWith(comments: updatedComments);
      _posts[postIndex] = updatedPost;

      // حفظ في Firebase
      final DatabaseReference postRef = _database.ref('posts/$postId');
      await postRef.update({
        'comments': updatedPost.comments.map((c) => c.toJson()).toList(),
        'updatedAt': DateTime.now().toIso8601String(),
      });

      notifyListeners();
      print('✅ تم إضافة الرد بنجاح');

      // إنشاء إشعار رد
      if (onCommentReplied != null && user != null) {
        try {
          final parentComment = post.comments[commentIndex];
          onCommentReplied!(parentComment.authorId, user, post.id, parentComment.id);
          print('💬 تم إرسال إشعار الرد');
        } catch (e) {
          print('❌ خطأ في إرسال إشعار الرد: $e');
        }
      }
    } catch (e) {
      print('❌ خطأ في إضافة الرد: $e');
      rethrow;
    }
  }

  // تبديل رد الفعل على الرد
  Future<void> toggleReplyReaction(
    String postId,
    String parentCommentId,
    String replyId,
    String userId,
    ReactionType reactionType,
  ) async {
    try {
      print('🔄 تبديل رد الفعل على الرد: $replyId');

      // العثور على المنشور والتعليق والرد
      final postIndex = _posts.indexWhere((p) => p.id == postId);
      if (postIndex == -1) return;

      final post = _posts[postIndex];
      final commentIndex = post.comments.indexWhere((c) => c.id == parentCommentId);
      if (commentIndex == -1) return;

      final comment = post.comments[commentIndex];
      final replyIndex = comment.replies.indexWhere((r) => r.id == replyId);
      if (replyIndex == -1) return;

      final reply = comment.replies[replyIndex];

      // تبديل رد الفعل
      final currentReaction = reply.reactions.getUserReaction(userId);

      ReactionSummary updatedReactions;
      if (currentReaction == reactionType) {
        // إزالة رد الفعل إذا كان نفس النوع
        updatedReactions = reply.reactions.removeReaction(userId);
      } else {
        // إضافة أو تغيير رد الفعل
        updatedReactions = reply.reactions.addReaction(userId, reactionType);
      }

      // تحديث الرد بردود الفعل الجديدة
      final updatedReply = reply.copyWith(reactions: updatedReactions);
      final updatedReplies = List<PostComment>.from(comment.replies);
      updatedReplies[replyIndex] = updatedReply;

      // تحديث التعليق بالردود المحدثة
      final updatedComment = comment.copyWith(replies: updatedReplies);
      final updatedComments = List<PostComment>.from(post.comments);
      updatedComments[commentIndex] = updatedComment;

      final updatedPost = post.copyWith(comments: updatedComments);
      _posts[postIndex] = updatedPost;

      // حفظ في Firebase
      final DatabaseReference postRef = _database.ref('posts/$postId');
      await postRef.update({
        'comments': updatedPost.comments.map((c) => c.toJson()).toList(),
        'updatedAt': DateTime.now().toIso8601String(),
      });

      notifyListeners();
      print('✅ تم تبديل رد الفعل على الرد');
    } catch (e) {
      print('❌ خطأ في تبديل رد الفعل على الرد: $e');
      rethrow;
    }
  }

  // الحصول على بيانات المستخدم الحالي
  Future<UserModel?> _getCurrentUserData(String userId) async {
    try {
      final userRef = _database.ref('users/$userId');
      final snapshot = await userRef.once();

      if (snapshot.snapshot.exists && snapshot.snapshot.value != null) {
        final userData = Map<String, dynamic>.from(snapshot.snapshot.value as Map);
        return UserModel.fromMap(userData);
      }
    } catch (e) {
      print('❌ خطأ في تحميل بيانات المستخدم: $e');
    }
    return null;
  }

  // فحص إمكانية رؤية المنشور حسب نوع الجمهور
  Future<bool> _canViewPost(Post post, UserModel? currentUser) async {
    // إذا لم يكن هناك مستخدم حالي، يرى فقط المنشورات العامة
    if (currentUser == null) {
      return post.audience == PostAudience.everyone;
    }

    // المؤلف يرى منشوراته دائماً
    if (post.authorId == currentUser.uid) {
      return true;
    }

    switch (post.audience) {
      case PostAudience.everyone:
        // المنشورات العامة - يراها الجميع
        return true;

      case PostAudience.followersOnly:
        // للمتابعين فقط - يجب أن يكون المستخدم الحالي متابعاً للمؤلف
        return await _isUserFollowingAuthor(currentUser.uid, post.authorId);

      case PostAudience.mutualFollowers:
        // للمتابعين المتبادلين - يجب أن يتابع كل منهما الآخر
        final isFollowing = await _isUserFollowingAuthor(currentUser.uid, post.authorId);
        final isFollowedBy = await _isUserFollowingAuthor(post.authorId, currentUser.uid);
        return isFollowing && isFollowedBy;
    }
  }

  // فحص إذا كان المستخدم يتابع المؤلف
  Future<bool> _isUserFollowingAuthor(String userId, String authorId) async {
    try {
      final userRef = _database.ref('users/$userId');
      final snapshot = await userRef.once();

      if (snapshot.snapshot.exists && snapshot.snapshot.value != null) {
        final userData = Map<String, dynamic>.from(snapshot.snapshot.value as Map);
        final following = List<String>.from(userData['following'] ?? []);
        return following.contains(authorId);
      }
    } catch (e) {
      print('❌ خطأ في فحص المتابعة: $e');
    }
    return false;
  }
}
